#!/usr/bin/env node

/**
 * SQLite setup script for CRM system testing
 * Alternative to PostgreSQL for local development/testing
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const setupSQLiteDatabase = async () => {
  console.log('🚀 Setting up CRM Database with SQLite...\n');

  const dbPath = path.join(__dirname, '..', 'data', 'crm_test.db');
  const dataDir = path.dirname(dbPath);

  try {
    // Create data directory if it doesn't exist
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // Create SQLite database
    const db = new sqlite3.Database(dbPath);

    console.log('1️⃣ Creating SQLite database...');
    console.log(`📊 Database path: ${dbPath}`);

    // Read and convert PostgreSQL schema to SQLite
    console.log('2️⃣ Creating database schema...');

    const sqliteSchema = `
      -- Users table
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'sales', 'support', 'user')),
        first_name TEXT,
        last_name TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      );

      -- Customers table
      CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        email TEXT UNIQUE NOT NULL,
        first_name TEXT,
        last_name TEXT,
        phone TEXT,
        company TEXT,
        lead_source TEXT DEFAULT 'website',
        lead_status TEXT DEFAULT 'new' CHECK (lead_status IN ('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost')),
        customer_type TEXT DEFAULT 'lead' CHECK (customer_type IN ('lead', 'prospect', 'customer', 'inactive')),
        assigned_to TEXT REFERENCES users(id),
        tags TEXT, -- JSON array as text
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_contact_date DATETIME
      );

      -- Form submissions table
      CREATE TABLE IF NOT EXISTS form_submissions (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        customer_id TEXT REFERENCES customers(id),
        form_type TEXT NOT NULL CHECK (form_type IN ('contact_form', 'consultation_request', 'service_booking', 'technical_support_request', 'quick_quote')),
        submission_data TEXT NOT NULL, -- JSON as text
        status TEXT DEFAULT 'new' CHECK (status IN ('new', 'processing', 'responded', 'completed', 'archived')),
        priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        reference_id TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        processed_at DATETIME,
        processed_by TEXT REFERENCES users(id)
      );

      -- Communications table
      CREATE TABLE IF NOT EXISTS communications (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        customer_id TEXT REFERENCES customers(id) NOT NULL,
        form_submission_id TEXT REFERENCES form_submissions(id),
        communication_type TEXT NOT NULL CHECK (communication_type IN ('email', 'phone', 'meeting', 'note', 'sms')),
        direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
        subject TEXT,
        content TEXT,
        sender_email TEXT,
        recipient_email TEXT,
        status TEXT DEFAULT 'sent' CHECK (status IN ('draft', 'sent', 'delivered', 'opened', 'clicked', 'replied', 'failed')),
        created_by TEXT REFERENCES users(id),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        scheduled_at DATETIME,
        sent_at DATETIME
      );

      -- Email templates table
      CREATE TABLE IF NOT EXISTS email_templates (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        name TEXT NOT NULL,
        subject TEXT NOT NULL,
        content TEXT NOT NULL,
        template_type TEXT NOT NULL,
        variables TEXT, -- JSON as text
        is_active BOOLEAN DEFAULT 1,
        created_by TEXT REFERENCES users(id),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Analytics events table
      CREATE TABLE IF NOT EXISTS analytics_events (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        event_type TEXT NOT NULL,
        event_data TEXT, -- JSON as text
        customer_id TEXT REFERENCES customers(id),
        user_id TEXT REFERENCES users(id),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Automation rules table
      CREATE TABLE IF NOT EXISTS automation_rules (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        name TEXT NOT NULL,
        trigger_type TEXT NOT NULL,
        trigger_conditions TEXT NOT NULL, -- JSON as text
        actions TEXT NOT NULL, -- JSON as text
        is_active BOOLEAN DEFAULT 1,
        created_by TEXT REFERENCES users(id),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_executed DATETIME
      );

      -- Indexes
      CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
      CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(lead_status);
      CREATE INDEX IF NOT EXISTS idx_form_submissions_customer_id ON form_submissions(customer_id);
      CREATE INDEX IF NOT EXISTS idx_communications_customer_id ON communications(customer_id);
    `;

    // Execute schema creation
    await new Promise((resolve, reject) => {
      db.exec(sqliteSchema, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ Schema created successfully');

    // Create admin user
    console.log('3️⃣ Creating initial admin user...');
    const bcrypt = require('bcryptjs');
    const adminPassword = await bcrypt.hash('admin123', 12);
    const adminId = 'admin-' + Date.now();

    await new Promise((resolve, reject) => {
      db.run(`
        INSERT OR IGNORE INTO users (id, username, email, password_hash, first_name, last_name, role)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [adminId, 'admin', '<EMAIL>', adminPassword, 'Admin', 'User', 'admin'], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ Admin user created (<EMAIL> / admin123)');

    // Create sample email templates
    console.log('4️⃣ Creating sample email templates...');
    const templates = [
      {
        id: 'template-1',
        name: 'Consultation Request Response',
        subject: 'Thank you for your consultation request, {customer_name}',
        content: 'Dear {customer_name},\n\nThank you for your consultation request...',
        type: 'consultation_request'
      }
    ];

    for (const template of templates) {
      await new Promise((resolve, reject) => {
        db.run(`
          INSERT OR IGNORE INTO email_templates (id, name, subject, content, template_type, variables)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [template.id, template.name, template.subject, template.content, template.type, '{}'], (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    }

    console.log('✅ Sample email templates created');

    // Close database
    db.close();

    console.log('\n🎉 SQLite database setup completed successfully!');
    console.log(`📊 Database location: ${dbPath}`);
    console.log('\n📝 Next steps:');
    console.log('   1. Update .env to use SQLite');
    console.log('   2. Start the CRM server: npm run dev');
    console.log('   3. Test the system: npm run test-system');

  } catch (error) {
    console.error('❌ SQLite setup failed:', error.message);
    process.exit(1);
  }
};

// Run setup if called directly
if (require.main === module) {
  setupSQLiteDatabase().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = { setupSQLiteDatabase };
