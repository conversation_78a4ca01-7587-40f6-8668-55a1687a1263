#!/usr/bin/env node

/**
 * Comprehensive CRM system testing script
 * Tests all major components and integrations
 */

const axios = require('axios');
const fs = require('fs');
require('dotenv').config();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const LM_STUDIO_URL = process.env.LMSTUDIO_URL || 'http://localhost:1234';

class CRMTester {
  constructor() {
    this.authToken = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runAllTests() {
    console.log('🧪 Starting CRM System Tests...\n');
    console.log(`🔗 API Base URL: ${API_BASE_URL}`);
    console.log(`🤖 LM Studio URL: ${LM_STUDIO_URL}\n`);

    try {
      // Test 1: Health Check
      await this.testHealthCheck();

      // Test 2: LM Studio Integration
      await this.testLMStudio();

      // Test 3: Authentication
      await this.testAuthentication();

      // Test 4: Form Submission
      await this.testFormSubmission();

      // Test 5: Customer Management
      await this.testCustomerManagement();

      // Test 6: Email System
      await this.testEmailSystem();

      // Test 7: Dashboard Analytics
      await this.testDashboard();

      // Test 8: AI Email Generation
      await this.testAIEmailGeneration();

      // Print results
      this.printResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async testHealthCheck() {
    console.log('1️⃣ Testing Health Check...');
    
    try {
      const response = await axios.get(`${API_BASE_URL}/health`, {
        timeout: 5000
      });

      if (response.status === 200 && response.data.status === 'healthy') {
        this.logSuccess('Health check passed');
      } else {
        this.logFailure('Health check failed', response.data);
      }
    } catch (error) {
      this.logFailure('Health check failed', error.message);
    }
  }

  async testLMStudio() {
    console.log('2️⃣ Testing LM Studio Integration...');
    
    try {
      const response = await axios.get(`${LM_STUDIO_URL}/v1/models`, {
        timeout: 5000
      });

      if (response.status === 200 && response.data.data) {
        this.logSuccess(`LM Studio connected - ${response.data.data.length} model(s) available`);
      } else {
        this.logFailure('LM Studio connection failed', 'No models found');
      }
    } catch (error) {
      this.logFailure('LM Studio connection failed', error.message);
    }
  }

  async testAuthentication() {
    console.log('3️⃣ Testing Authentication...');
    
    try {
      // Test login
      const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });

      if (loginResponse.status === 200 && loginResponse.data.data.token) {
        this.authToken = loginResponse.data.data.token;
        this.logSuccess('Admin login successful');

        // Test protected route
        const meResponse = await axios.get(`${API_BASE_URL}/api/auth/me`, {
          headers: { Authorization: `Bearer ${this.authToken}` }
        });

        if (meResponse.status === 200) {
          this.logSuccess('Protected route access successful');
        } else {
          this.logFailure('Protected route access failed');
        }
      } else {
        this.logFailure('Admin login failed', loginResponse.data);
      }
    } catch (error) {
      this.logFailure('Authentication test failed', error.response?.data?.message || error.message);
    }
  }

  async testFormSubmission() {
    console.log('4️⃣ Testing Form Submission...');
    
    try {
      const formData = {
        email: '<EMAIL>',
        name: 'Test Customer',
        phone: '+1234567890',
        company: 'Test Company',
        form_type: 'consultation_request',
        consultation_type: 'ML Trading System',
        project_description: 'Need help with cryptocurrency trading algorithms',
        budget_range: '$10,000 - $25,000',
        timeline: '3-6 months'
      };

      const response = await axios.post(`${API_BASE_URL}/api/forms/submit`, formData);

      if (response.status === 200 && response.data.success) {
        this.logSuccess('Form submission successful');
        this.testCustomerId = response.data.data.customer_id;
      } else {
        this.logFailure('Form submission failed', response.data);
      }
    } catch (error) {
      this.logFailure('Form submission failed', error.response?.data?.message || error.message);
    }
  }

  async testCustomerManagement() {
    console.log('5️⃣ Testing Customer Management...');
    
    if (!this.authToken) {
      this.logFailure('Customer management test skipped', 'No auth token');
      return;
    }

    try {
      // Get customers list
      const customersResponse = await axios.get(`${API_BASE_URL}/api/customers`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (customersResponse.status === 200 && customersResponse.data.data.customers) {
        this.logSuccess(`Customer list retrieved - ${customersResponse.data.data.customers.length} customers`);

        // Test customer details if we have a customer
        if (this.testCustomerId) {
          const customerResponse = await axios.get(
            `${API_BASE_URL}/api/customers/${this.testCustomerId}`,
            { headers: { Authorization: `Bearer ${this.authToken}` } }
          );

          if (customerResponse.status === 200) {
            this.logSuccess('Customer details retrieved');
          } else {
            this.logFailure('Customer details retrieval failed');
          }
        }
      } else {
        this.logFailure('Customer list retrieval failed');
      }
    } catch (error) {
      this.logFailure('Customer management test failed', error.response?.data?.message || error.message);
    }
  }

  async testEmailSystem() {
    console.log('6️⃣ Testing Email System...');
    
    try {
      // Test email templates
      const templatesResponse = await axios.get(`${API_BASE_URL}/api/communications/templates`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (templatesResponse.status === 200) {
        this.logSuccess(`Email templates retrieved - ${templatesResponse.data.data.length} templates`);
      } else {
        this.logFailure('Email templates retrieval failed');
      }
    } catch (error) {
      this.logFailure('Email system test failed', error.response?.data?.message || error.message);
    }
  }

  async testDashboard() {
    console.log('7️⃣ Testing Dashboard Analytics...');
    
    if (!this.authToken) {
      this.logFailure('Dashboard test skipped', 'No auth token');
      return;
    }

    try {
      const statsResponse = await axios.get(`${API_BASE_URL}/api/dashboard/stats`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (statsResponse.status === 200 && statsResponse.data.data) {
        this.logSuccess('Dashboard statistics retrieved');
        console.log(`   📊 Total Customers: ${statsResponse.data.data.totalCustomers}`);
        console.log(`   📧 Emails Sent: ${statsResponse.data.data.emailsSent}`);
      } else {
        this.logFailure('Dashboard statistics retrieval failed');
      }
    } catch (error) {
      this.logFailure('Dashboard test failed', error.response?.data?.message || error.message);
    }
  }

  async testAIEmailGeneration() {
    console.log('8️⃣ Testing AI Email Generation...');
    
    if (!this.authToken || !this.testCustomerId) {
      this.logFailure('AI email test skipped', 'Missing auth token or customer ID');
      return;
    }

    try {
      const aiResponse = await axios.post(
        `${API_BASE_URL}/api/communications/generate-response`,
        {
          customerId: this.testCustomerId,
          formType: 'consultation_request',
          context: { subject: 'Test AI Response' }
        },
        { headers: { Authorization: `Bearer ${this.authToken}` } }
      );

      if (aiResponse.status === 200 && aiResponse.data.data.content) {
        this.logSuccess('AI email generation successful');
        console.log(`   🤖 Provider: ${aiResponse.data.data.provider}`);
        console.log(`   📝 Content length: ${aiResponse.data.data.content.length} characters`);
      } else {
        this.logFailure('AI email generation failed');
      }
    } catch (error) {
      this.logFailure('AI email generation failed', error.response?.data?.message || error.message);
    }
  }

  logSuccess(message) {
    console.log(`   ✅ ${message}`);
    this.testResults.passed++;
    this.testResults.tests.push({ status: 'PASS', message });
  }

  logFailure(message, details = '') {
    console.log(`   ❌ ${message}`);
    if (details) {
      console.log(`      Details: ${typeof details === 'object' ? JSON.stringify(details) : details}`);
    }
    this.testResults.failed++;
    this.testResults.tests.push({ status: 'FAIL', message, details });
  }

  printResults() {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📈 Success Rate: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`);

    if (this.testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.tests
        .filter(test => test.status === 'FAIL')
        .forEach(test => {
          console.log(`   - ${test.message}`);
        });
    }

    console.log('\n🎉 Testing completed!');
    
    if (this.testResults.failed === 0) {
      console.log('🚀 All tests passed! Your CRM system is ready to use.');
    } else {
      console.log('⚠️ Some tests failed. Please check the configuration and try again.');
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new CRMTester();
  tester.runAllTests().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = CRMTester;
