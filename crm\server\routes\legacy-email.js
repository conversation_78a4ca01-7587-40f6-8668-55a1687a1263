const express = require('express');
const fetch = require('node-fetch');
const router = express.Router();

/**
 * Legacy email routes for backward compatibility
 * These routes maintain compatibility with existing portfolio forms
 */

/**
 * Legacy consultation confirmation
 * POST /api/email/consultation-confirmation
 */
router.post('/consultation-confirmation', async (req, res) => {
  try {
    // Redirect to new form submission endpoint
    const formData = {
      ...req.body,
      form_type: 'consultation_request'
    };

    // Forward to new forms API
    const forwardResponse = await fetch(`${req.protocol}://${req.get('host')}/api/forms/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await forwardResponse.json();
    res.status(forwardResponse.status).json(result);
  } catch (error) {
    console.error('Legacy consultation confirmation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process consultation request'
    });
  }
});

/**
 * Legacy support confirmation
 * POST /api/email/support-confirmation
 */
router.post('/support-confirmation', async (req, res) => {
  try {
    const formData = {
      ...req.body,
      form_type: 'technical_support_request'
    };

    const forwardResponse = await fetch(`${req.protocol}://${req.get('host')}/api/forms/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await forwardResponse.json();
    res.status(forwardResponse.status).json(result);
  } catch (error) {
    console.error('Legacy support confirmation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process support request'
    });
  }
});

/**
 * Legacy service booking confirmation
 * POST /api/email/service-booking-confirmation
 */
router.post('/service-booking-confirmation', async (req, res) => {
  try {
    const formData = {
      ...req.body,
      form_type: 'service_booking'
    };

    const forwardResponse = await fetch(`${req.protocol}://${req.get('host')}/api/forms/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await forwardResponse.json();
    res.status(forwardResponse.status).json(result);
  } catch (error) {
    console.error('Legacy service booking confirmation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process service booking'
    });
  }
});

/**
 * Legacy contact form
 * POST /api/email/contact-form
 */
router.post('/contact-form', async (req, res) => {
  try {
    const formData = {
      ...req.body,
      form_type: 'contact_form'
    };

    const forwardResponse = await fetch(`${req.protocol}://${req.get('host')}/api/forms/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await forwardResponse.json();
    res.status(forwardResponse.status).json(result);
  } catch (error) {
    console.error('Legacy contact form error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process contact form'
    });
  }
});

/**
 * Legacy quick quote
 * POST /api/email/quick-quote
 */
router.post('/quick-quote', async (req, res) => {
  try {
    const formData = {
      ...req.body,
      form_type: 'quick_quote'
    };

    const forwardResponse = await fetch(`${req.protocol}://${req.get('host')}/api/forms/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const result = await forwardResponse.json();
    res.status(forwardResponse.status).json(result);
  } catch (error) {
    console.error('Legacy quick quote error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process quick quote'
    });
  }
});

module.exports = router;
