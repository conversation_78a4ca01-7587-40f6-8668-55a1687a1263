import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaU<PERSON>s, FaEnvelope, FaChartLine, FaCog, FaSearch,
  FaFilter, FaDownload, FaPlus, FaEye, FaEdit, FaTrash
} from 'react-icons/fa';
import './CRMDashboard.css';

// Import sub-components
import CustomerList from './CustomerList';
import FormSubmissions from './FormSubmissions';
import Analytics from './Analytics';
import EmailAutomation from './EmailAutomation';
import Settings from './Settings';

const CRMDashboard = () => {
  const [activeTab, setActiveTab] = useState('customers');
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({
    totalCustomers: 0,
    newLeads: 0,
    pendingForms: 0,
    emailsSent: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load user info
      const userResponse = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('crmToken')}`
        }
      });
      
      if (userResponse.ok) {
        const userData = await userResponse.json();
        setUser(userData.data);
      }

      // Load dashboard stats
      const statsResponse = await fetch('/api/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('crmToken')}`
        }
      });
      
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'customers', label: 'Customers', icon: FaUsers },
    { id: 'forms', label: 'Form Submissions', icon: FaEnvelope },
    { id: 'analytics', label: 'Analytics', icon: FaChartLine },
    { id: 'automation', label: 'Email Automation', icon: FaCog },
    { id: 'settings', label: 'Settings', icon: FaCog }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'customers':
        return <CustomerList onStatsUpdate={loadDashboardData} />;
      case 'forms':
        return <FormSubmissions onStatsUpdate={loadDashboardData} />;
      case 'analytics':
        return <Analytics />;
      case 'automation':
        return <EmailAutomation />;
      case 'settings':
        return <Settings />;
      default:
        return <CustomerList onStatsUpdate={loadDashboardData} />;
    }
  };

  if (loading) {
    return (
      <div className="crm-loading">
        <div className="loading-spinner"></div>
        <p>Loading CRM Dashboard...</p>
      </div>
    );
  }

  return (
    <div className="crm-dashboard">
      {/* Header */}
      <header className="crm-header">
        <div className="header-left">
          <h1>CRM Dashboard</h1>
          <p>Welcome back, {user?.first_name || 'User'}</p>
        </div>
        <div className="header-right">
          <button className="btn-export" onClick={() => window.print()}>
            <FaDownload /> Export
          </button>
          <div className="user-menu">
            <img 
              src={`https://ui-avatars.com/api/?name=${user?.first_name}+${user?.last_name}&background=0D8ABC&color=fff`}
              alt="User Avatar"
              className="user-avatar"
            />
            <span>{user?.first_name} {user?.last_name}</span>
          </div>
        </div>
      </header>

      {/* Stats Cards */}
      <div className="stats-grid">
        <motion.div 
          className="stat-card"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <div className="stat-icon customers">
            <FaUsers />
          </div>
          <div className="stat-content">
            <h3>{stats.totalCustomers}</h3>
            <p>Total Customers</p>
          </div>
        </motion.div>

        <motion.div 
          className="stat-card"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <div className="stat-icon leads">
            <FaPlus />
          </div>
          <div className="stat-content">
            <h3>{stats.newLeads}</h3>
            <p>New Leads</p>
          </div>
        </motion.div>

        <motion.div 
          className="stat-card"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <div className="stat-icon forms">
            <FaEnvelope />
          </div>
          <div className="stat-content">
            <h3>{stats.pendingForms}</h3>
            <p>Pending Forms</p>
          </div>
        </motion.div>

        <motion.div 
          className="stat-card"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <div className="stat-icon emails">
            <FaChartLine />
          </div>
          <div className="stat-content">
            <h3>{stats.emailsSent}</h3>
            <p>Emails Sent</p>
          </div>
        </motion.div>
      </div>

      {/* Navigation Tabs */}
      <nav className="crm-nav">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <tab.icon />
            <span>{tab.label}</span>
          </button>
        ))}
      </nav>

      {/* Main Content */}
      <main className="crm-content">
        {renderTabContent()}
      </main>
    </div>
  );
};

export default CRMDashboard;
