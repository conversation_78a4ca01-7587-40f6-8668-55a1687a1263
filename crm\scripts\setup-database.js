#!/usr/bin/env node

/**
 * Database setup script for CRM system
 * Creates database, runs migrations, and seeds initial data
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const setupDatabase = async () => {
  console.log('🚀 Setting up CRM Database...\n');

  // Database configuration
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: 'postgres' // Connect to default database first
  };

  const targetDatabase = process.env.DB_NAME || 'portfolio_crm';

  let pool = new Pool(dbConfig);

  try {
    // Step 1: Check if target database exists
    console.log('1️⃣ Checking if database exists...');
    const dbCheckResult = await pool.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [targetDatabase]
    );

    if (dbCheckResult.rows.length === 0) {
      console.log(`📊 Creating database: ${targetDatabase}`);
      await pool.query(`CREATE DATABASE "${targetDatabase}"`);
      console.log('✅ Database created successfully\n');
    } else {
      console.log('✅ Database already exists\n');
    }

    // Close connection to default database
    await pool.end();

    // Step 2: Connect to target database
    console.log('2️⃣ Connecting to target database...');
    pool = new Pool({
      ...dbConfig,
      database: targetDatabase
    });

    // Test connection
    await pool.query('SELECT NOW()');
    console.log('✅ Connected to target database\n');

    // Step 3: Run schema creation
    console.log('3️⃣ Creating database schema...');
    const schemaSQL = fs.readFileSync(
      path.join(__dirname, '..', 'database', 'schema.sql'),
      'utf8'
    );

    // Split by semicolon and execute each statement
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      try {
        await pool.query(statement);
      } catch (error) {
        // Ignore "already exists" errors
        if (!error.message.includes('already exists')) {
          console.warn(`⚠️ Warning executing statement: ${error.message}`);
        }
      }
    }
    console.log('✅ Schema created successfully\n');

    // Step 4: Create initial admin user
    console.log('4️⃣ Creating initial admin user...');
    const bcrypt = require('bcryptjs');
    const adminPassword = await bcrypt.hash('admin123', 12);

    try {
      await pool.query(`
        INSERT INTO users (username, email, password_hash, first_name, last_name, role)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (email) DO NOTHING
      `, ['admin', '<EMAIL>', adminPassword, 'Admin', 'User', 'admin']);

      console.log('✅ Admin user created (<EMAIL> / admin123)\n');
    } catch (error) {
      console.log('ℹ️ Admin user already exists\n');
    }

    // Step 5: Create sample email templates
    console.log('5️⃣ Creating sample email templates...');
    const templates = [
      {
        name: 'Consultation Request Response',
        subject: 'Thank you for your consultation request, {customer_name}',
        content: `Dear {customer_name},

Thank you for your consultation request. I've received your inquiry and will review your requirements carefully.

I'll get back to you within 24 hours to discuss your project and schedule a consultation.

Best regards,
DJ Martin
Real-Time ML Systems Architect`,
        type: 'consultation_request'
      },
      {
        name: 'Technical Support Response',
        subject: 'Your technical support request - {customer_name}',
        content: `Dear {customer_name},

Thank you for your technical support request. I've received your inquiry and will address it promptly.

For urgent issues, I aim to respond within 2-4 hours during business hours.

Best regards,
DJ Martin`,
        type: 'technical_support_request'
      }
    ];

    for (const template of templates) {
      try {
        await pool.query(`
          INSERT INTO email_templates (name, subject, content, template_type, variables)
          VALUES ($1, $2, $3, $4, $5)
          ON CONFLICT DO NOTHING
        `, [
          template.name,
          template.subject,
          template.content,
          template.type,
          JSON.stringify({ customer_name: 'Customer name' })
        ]);
      } catch (error) {
        // Template might already exist
      }
    }
    console.log('✅ Sample email templates created\n');

    // Step 6: Verify setup
    console.log('6️⃣ Verifying database setup...');
    const tableCheck = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    console.log('📋 Created tables:');
    tableCheck.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Start the CRM server: npm run dev');
    console.log('   2. Test the health check: npm run check-lmstudio');
    console.log('   3. Access the API at: http://localhost:3001');
    console.log('\n🔐 Admin credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('   ⚠️ Change this password immediately in production!');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    console.error('❌ Error details:', error);
    console.error('\n🔧 Troubleshooting tips:');
    console.error('   1. Ensure PostgreSQL is running');
    console.error('   2. Check database credentials in .env file');
    console.error('   3. Verify database user has CREATE privileges');
    console.error('   4. Try connecting manually: psql -h localhost -U postgres');
    process.exit(1);
  } finally {
    if (pool) {
      await pool.end();
    }
  }
};

// Run setup if called directly
if (require.main === module) {
  setupDatabase().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = { setupDatabase };
