const { query } = require('../../config/database');

/**
 * Analytics service for tracking and analyzing CRM data
 */
class AnalyticsService {
  
  /**
   * Log analytics event
   */
  async logEvent(eventType, eventData, customerId = null, userId = null) {
    try {
      await query(`
        INSERT INTO analytics_events (event_type, event_data, customer_id, user_id)
        VALUES ($1, $2, $3, $4)
      `, [eventType, JSON.stringify(eventData), customerId, userId]);
      
      console.log(`📊 Analytics event logged: ${eventType}`);
    } catch (error) {
      console.error('Analytics logging error:', error);
    }
  }

  /**
   * Get form submission analytics
   */
  async getFormAnalytics(timeframe = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeframe);
      
      // Form submissions by type
      const submissionsByType = await query(`
        SELECT 
          form_type,
          COUNT(*) as total_submissions,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN status = 'new' THEN 1 END) as pending,
          ROUND(AVG(EXTRACT(EPOCH FROM (COALESCE(processed_at, NOW()) - submitted_at))/3600), 2) as avg_response_hours
        FROM form_submissions 
        WHERE submitted_at >= $1
        GROUP BY form_type
        ORDER BY total_submissions DESC
      `, [dateFilter]);

      // Daily submission trends
      const dailyTrends = await query(`
        SELECT 
          DATE(submitted_at) as date,
          form_type,
          COUNT(*) as submissions
        FROM form_submissions 
        WHERE submitted_at >= $1
        GROUP BY DATE(submitted_at), form_type
        ORDER BY date, form_type
      `, [dateFilter]);

      // Conversion rates by form type
      const conversionRates = await query(`
        SELECT 
          fs.form_type,
          COUNT(*) as total_submissions,
          COUNT(CASE WHEN c.lead_status = 'closed_won' THEN 1 END) as conversions,
          ROUND((COUNT(CASE WHEN c.lead_status = 'closed_won' THEN 1 END)::float / COUNT(*) * 100), 2) as conversion_rate
        FROM form_submissions fs
        JOIN customers c ON fs.customer_id = c.id
        WHERE fs.submitted_at >= $1
        GROUP BY fs.form_type
        ORDER BY conversion_rate DESC
      `, [dateFilter]);

      return {
        submissionsByType: submissionsByType.rows,
        dailyTrends: dailyTrends.rows,
        conversionRates: conversionRates.rows,
        timeframe
      };
    } catch (error) {
      console.error('Form analytics error:', error);
      throw error;
    }
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(timeframe = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeframe);

      // Customer acquisition trends
      const acquisitionTrends = await query(`
        SELECT 
          DATE(created_at) as date,
          lead_source,
          COUNT(*) as new_customers
        FROM customers 
        WHERE created_at >= $1 AND customer_type != 'inactive'
        GROUP BY DATE(created_at), lead_source
        ORDER BY date, lead_source
      `, [dateFilter]);

      // Lead status distribution
      const statusDistribution = await query(`
        SELECT 
          lead_status,
          COUNT(*) as count,
          ROUND((COUNT(*)::float / (SELECT COUNT(*) FROM customers WHERE customer_type != 'inactive') * 100), 2) as percentage
        FROM customers 
        WHERE customer_type != 'inactive'
        GROUP BY lead_status
        ORDER BY count DESC
      `);

      // Customer lifetime value (simplified)
      const lifetimeValue = await query(`
        SELECT 
          AVG(CASE WHEN lead_status = 'closed_won' THEN 1 ELSE 0 END) as avg_conversion_rate,
          COUNT(CASE WHEN lead_status = 'closed_won' THEN 1 END) as total_conversions,
          COUNT(*) as total_customers
        FROM customers 
        WHERE created_at >= $1 AND customer_type != 'inactive'
      `, [dateFilter]);

      // Top lead sources
      const topSources = await query(`
        SELECT 
          lead_source,
          COUNT(*) as total_leads,
          COUNT(CASE WHEN lead_status = 'closed_won' THEN 1 END) as conversions,
          ROUND((COUNT(CASE WHEN lead_status = 'closed_won' THEN 1 END)::float / COUNT(*) * 100), 2) as conversion_rate
        FROM customers 
        WHERE created_at >= $1 AND customer_type != 'inactive'
        GROUP BY lead_source
        ORDER BY total_leads DESC
      `, [dateFilter]);

      return {
        acquisitionTrends: acquisitionTrends.rows,
        statusDistribution: statusDistribution.rows,
        lifetimeValue: lifetimeValue.rows[0],
        topSources: topSources.rows,
        timeframe
      };
    } catch (error) {
      console.error('Customer analytics error:', error);
      throw error;
    }
  }

  /**
   * Get communication analytics
   */
  async getCommunicationAnalytics(timeframe = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeframe);

      // Email performance metrics
      const emailMetrics = await query(`
        SELECT 
          COUNT(*) as total_sent,
          COUNT(CASE WHEN status IN ('delivered', 'opened', 'clicked') THEN 1 END) as delivered,
          COUNT(CASE WHEN status IN ('opened', 'clicked') THEN 1 END) as opened,
          COUNT(CASE WHEN status = 'clicked' THEN 1 END) as clicked,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
          ROUND((COUNT(CASE WHEN status IN ('delivered', 'opened', 'clicked') THEN 1 END)::float / COUNT(*) * 100), 2) as delivery_rate,
          ROUND((COUNT(CASE WHEN status IN ('opened', 'clicked') THEN 1 END)::float / COUNT(*) * 100), 2) as open_rate,
          ROUND((COUNT(CASE WHEN status = 'clicked' THEN 1 END)::float / COUNT(*) * 100), 2) as click_rate
        FROM communications 
        WHERE communication_type = 'email' 
        AND direction = 'outbound' 
        AND created_at >= $1
      `, [dateFilter]);

      // Communication volume by type
      const volumeByType = await query(`
        SELECT 
          communication_type,
          direction,
          COUNT(*) as count
        FROM communications 
        WHERE created_at >= $1
        GROUP BY communication_type, direction
        ORDER BY count DESC
      `, [dateFilter]);

      // Response time analysis
      const responseTime = await query(`
        SELECT 
          AVG(EXTRACT(EPOCH FROM (processed_at - submitted_at))/3600) as avg_response_hours,
          MIN(EXTRACT(EPOCH FROM (processed_at - submitted_at))/3600) as min_response_hours,
          MAX(EXTRACT(EPOCH FROM (processed_at - submitted_at))/3600) as max_response_hours
        FROM form_submissions 
        WHERE processed_at IS NOT NULL 
        AND submitted_at >= $1
      `, [dateFilter]);

      return {
        emailMetrics: emailMetrics.rows[0],
        volumeByType: volumeByType.rows,
        responseTime: responseTime.rows[0],
        timeframe
      };
    } catch (error) {
      console.error('Communication analytics error:', error);
      throw error;
    }
  }

  /**
   * Get AI performance analytics
   */
  async getAIAnalytics(timeframe = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeframe);

      // AI usage statistics
      const aiUsage = await query(`
        SELECT 
          COUNT(*) as total_ai_requests,
          COUNT(CASE WHEN event_data->>'provider' = 'lmstudio' THEN 1 END) as lmstudio_requests,
          COUNT(CASE WHEN event_data->>'provider' = 'ollama' THEN 1 END) as ollama_requests,
          COUNT(CASE WHEN event_data->>'provider' = 'openai' THEN 1 END) as openai_requests,
          AVG(CAST(event_data->>'response_time' AS FLOAT)) as avg_response_time
        FROM analytics_events 
        WHERE event_type = 'ai_generation' 
        AND created_at >= $1
      `, [dateFilter]);

      // AI success rate
      const aiSuccessRate = await query(`
        SELECT 
          COUNT(*) as total_attempts,
          COUNT(CASE WHEN event_data->>'success' = 'true' THEN 1 END) as successful,
          ROUND((COUNT(CASE WHEN event_data->>'success' = 'true' THEN 1 END)::float / COUNT(*) * 100), 2) as success_rate
        FROM analytics_events 
        WHERE event_type = 'ai_generation' 
        AND created_at >= $1
      `, [dateFilter]);

      return {
        usage: aiUsage.rows[0],
        successRate: aiSuccessRate.rows[0],
        timeframe
      };
    } catch (error) {
      console.error('AI analytics error:', error);
      throw error;
    }
  }

  /**
   * Get system performance analytics
   */
  async getPerformanceAnalytics(timeframe = '30d') {
    try {
      const dateFilter = this.getDateFilter(timeframe);

      // API performance metrics
      const apiPerformance = await query(`
        SELECT 
          event_data->>'method' as method,
          event_data->>'url' as url,
          COUNT(*) as request_count,
          AVG(CAST(event_data->>'duration' AS FLOAT)) as avg_duration,
          MAX(CAST(event_data->>'duration' AS FLOAT)) as max_duration,
          COUNT(CASE WHEN CAST(event_data->>'statusCode' AS INT) >= 400 THEN 1 END) as error_count
        FROM analytics_events 
        WHERE event_type = 'performance_metric' 
        AND created_at >= $1
        GROUP BY event_data->>'method', event_data->>'url'
        ORDER BY request_count DESC
        LIMIT 20
      `, [dateFilter]);

      // Error rate analysis
      const errorRates = await query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as total_requests,
          COUNT(CASE WHEN CAST(event_data->>'statusCode' AS INT) >= 400 THEN 1 END) as errors,
          ROUND((COUNT(CASE WHEN CAST(event_data->>'statusCode' AS INT) >= 400 THEN 1 END)::float / COUNT(*) * 100), 2) as error_rate
        FROM analytics_events 
        WHERE event_type = 'performance_metric' 
        AND created_at >= $1
        GROUP BY DATE(created_at)
        ORDER BY date
      `, [dateFilter]);

      return {
        apiPerformance: apiPerformance.rows,
        errorRates: errorRates.rows,
        timeframe
      };
    } catch (error) {
      console.error('Performance analytics error:', error);
      throw error;
    }
  }

  /**
   * Generate comprehensive analytics report
   */
  async generateReport(timeframe = '30d') {
    try {
      const [
        formAnalytics,
        customerAnalytics,
        communicationAnalytics,
        aiAnalytics,
        performanceAnalytics
      ] = await Promise.all([
        this.getFormAnalytics(timeframe),
        this.getCustomerAnalytics(timeframe),
        this.getCommunicationAnalytics(timeframe),
        this.getAIAnalytics(timeframe),
        this.getPerformanceAnalytics(timeframe)
      ]);

      return {
        generatedAt: new Date().toISOString(),
        timeframe,
        forms: formAnalytics,
        customers: customerAnalytics,
        communications: communicationAnalytics,
        ai: aiAnalytics,
        performance: performanceAnalytics
      };
    } catch (error) {
      console.error('Report generation error:', error);
      throw error;
    }
  }

  /**
   * Get date filter based on timeframe
   */
  getDateFilter(timeframe) {
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      case '1y':
        return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }
}

/**
 * Helper function to log analytics events
 */
const logAnalyticsEvent = async (eventType, eventData, customerId = null, userId = null) => {
  const analyticsService = new AnalyticsService();
  return await analyticsService.logEvent(eventType, eventData, customerId, userId);
};

module.exports = {
  AnalyticsService,
  logAnalyticsEvent
};
