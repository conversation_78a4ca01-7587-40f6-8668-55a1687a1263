.buy-time-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.buy-time-form-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(16, 185, 129, 0.3);
  padding: 2rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(16, 185, 129, 0.6) rgba(15, 23, 42, 0.3);
}

/* Custom scrollbar for Webkit browsers */
.buy-time-form-container::-webkit-scrollbar {
  width: 6px;
}

.buy-time-form-container::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 10px;
}

.buy-time-form-container::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.6);
  border-radius: 10px;
}

.buy-time-form-container::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.8);
}

.buy-time-form-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.buy-time-form-close:hover {
  color: #e2e8f0;
  background: rgba(148, 163, 184, 0.1);
}

.buy-time-form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.buy-time-form-header h2 {
  color: #10b981;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.buy-time-form-subtitle {
  color: #94a3b8;
  font-size: 1rem;
}

.buy-time-form-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.buy-time-form-progress::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(148, 163, 184, 0.2);
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #1e293b;
  border: 2px solid #94a3b8;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.step-label {
  font-size: 0.85rem;
  color: #94a3b8;
}

.progress-step.active .step-number {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
  color: #10b981;
}

.progress-step.active .step-label {
  color: #e2e8f0;
}

.progress-step.current .step-number {
  background-color: #10b981;
  color: #fff;
}

.buy-time-form-content {
  color: #e2e8f0;
}

.buy-time-form-step h3 {
  color: #e2e8f0;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  text-align: center;
}

.step-description {
  color: #94a3b8;
  font-size: 0.95rem;
  margin: -0.5rem 0 1.5rem 0;
  text-align: center;
}

.issue-type-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.2rem;
  margin-bottom: 1.5rem;
}

.issue-type-option {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1.2rem;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.issue-type-option:hover {
  background: rgba(15, 23, 42, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

.issue-type-option.selected {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.issue-type-icon {
  font-size: 2rem;
  color: #10b981;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(16, 185, 129, 0.1);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto 1rem auto;
}

.issue-type-option h4 {
  color: #e2e8f0;
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  text-align: center;
}

.issue-type-option p {
  color: #94a3b8;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  text-align: center;
}

.issue-examples {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 8px;
  padding: 0.8rem;
  margin-top: 0.8rem;
}

.issue-examples strong {
  color: #cbd5e1;
  font-size: 0.85rem;
  display: block;
  margin-bottom: 0.5rem;
}

.issue-examples ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #94a3b8;
  font-size: 0.8rem;
}

.issue-examples li {
  margin-bottom: 0.3rem;
}

.issue-type-selected-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #10b981;
  color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.service-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.service-option {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.service-option:hover {
  transform: translateY(-3px);
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.service-option.selected {
  background: rgba(16, 185, 129, 0.05);
  border-color: #10b981;
}

.service-option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.service-option-header h4 {
  color: #e2e8f0;
  margin: 0;
  font-size: 1.1rem;
}

.service-price {
  color: #10b981;
  font-weight: 600;
  font-size: 1.1rem;
}

.service-description {
  color: #94a3b8;
  font-size: 0.9rem;
  margin: 0 0 0.8rem 0;
}

.service-details {
  background: rgba(15, 23, 42, 0.7);
  border-radius: 8px;
  padding: 0.8rem;
  margin-top: 0.8rem;
  border-left: 3px solid rgba(16, 185, 129, 0.5);
}

.service-time-estimate {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
  font-size: 0.85rem;
  margin: 0 0 0.5rem 0;
}

.service-detailed-description {
  color: #cbd5e1;
  font-size: 0.85rem;
  margin: 0;
  line-height: 1.4;
}

.service-selected-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 24px;
  height: 24px;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group.half {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #cbd5e1;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border-radius: 8px;
  background-color: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #e2e8f0;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.custom-time-input {
  display: flex;
  align-items: center;
  margin-top: 0.8rem;
  background-color: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.custom-time-input input {
  flex: 1;
  border: none;
  border-radius: 0;
  background: none;
}

.custom-time-input span {
  padding: 0 1rem;
  color: #94a3b8;
  background-color: rgba(15, 23, 42, 0.8);
  height: 100%;
  display: flex;
  align-items: center;
}

.urgency-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.8rem;
}

.urgency-option {
  padding: 0.8rem;
  border-radius: 8px;
  background-color: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #e2e8f0;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.urgency-option:hover {
  background-color: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.3);
}

.urgency-option.selected {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
  color: #10b981;
}

.urgency-icon {
  color: #f59e0b;
  font-size: 1.2rem;
}

.urgency-multiplier {
  font-size: 0.8rem;
  color: #f59e0b;
}

.price-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.price-label {
  font-size: 1.1rem;
  color: #e2e8f0;
}

.price-amount {
  font-size: 1.5rem;
  font-weight: 600;
  color: #10b981;
}

.schedule-note,
.payment-note {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  margin-bottom: 1.5rem;
}

.schedule-note svg,
.payment-note svg {
  color: #10b981;
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.schedule-note p,
.payment-note p {
  margin: 0;
  color: #cbd5e1;
  font-size: 0.95rem;
}

.step-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.back-btn,
.next-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn {
  background: rgba(15, 23, 42, 0.5);
  color: #cbd5e1;
  border: 1px solid rgba(203, 213, 225, 0.3);
}

.back-btn:hover {
  background: rgba(15, 23, 42, 0.7);
  color: #e2e8f0;
}

.next-btn {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.next-btn:hover:not(:disabled) {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
}

.next-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Success step */
.success-step {
  text-align: center;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: #10b981;
  border: 2px solid rgba(16, 185, 129, 0.3);
}

.success-step h3 {
  color: #10b981;
  margin-bottom: 1rem;
}

.success-message {
  color: #cbd5e1;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.purchase-summary {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.purchase-summary h4 {
  color: #e2e8f0;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(16, 185, 129, 0.2);
}

.summary-item {
  display: flex;
  margin-bottom: 0.8rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item span:first-child {
  width: 120px;
  color: #94a3b8;
  font-size: 0.9rem;
}

.summary-item span:last-child {
  flex: 1;
  color: #e2e8f0;
}

.description-summary {
  font-style: italic;
  font-size: 0.85rem;
  line-height: 1.4;
  display: block;
  margin-top: 0.3rem;
}

.summary-item.total {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(16, 185, 129, 0.2);
}

.summary-item.total span {
  font-weight: 600;
  font-size: 1.1rem;
}

.summary-item.total span:last-child {
  color: #10b981;
}

.next-steps {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
  margin-bottom: 2rem;
}

.next-steps h4 {
  color: #e2e8f0;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.next-steps ol {
  color: #cbd5e1;
  margin: 0;
  padding-left: 1.5rem;
}

.next-steps li {
  margin-bottom: 0.8rem;
}

.next-steps li:last-child {
  margin-bottom: 0;
}

.close-btn {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
}

/* Responsive styles */
@media (max-width: 768px) {
  .buy-time-form-container {
    padding: 1.5rem;
    width: 95%;
    max-height: 90vh;
  }

  .step-label {
    display: none;
  }

  .service-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .service-option {
    padding: 1.5rem;
    min-height: 44px;
  }

  .urgency-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .urgency-option {
    padding: 1.2rem;
    min-height: 44px;
  }

  .form-row {
    flex-direction: column;
    gap: 1.5rem;
  }

  .summary-item {
    flex-direction: column;
  }

  .summary-item span:first-child {
    width: 100%;
    margin-bottom: 0.3rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 44px;
    font-size: 1rem;
    padding: 12px;
  }

  .btn-primary,
  .btn-secondary {
    min-height: 48px;
    padding: 12px 24px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .buy-time-form-container {
    width: 98%;
    padding: 1rem;
    max-height: 95vh;
  }

  .service-option,
  .urgency-option {
    padding: 1rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 46px;
    padding: 14px;
  }

  .btn-primary,
  .btn-secondary {
    min-height: 50px;
    padding: 14px 24px;
  }
}

@media (max-width: 375px) {
  .buy-time-form-container {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    padding: 1rem;
  }

  .buy-time-form-overlay {
    align-items: flex-start;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 48px;
    padding: 16px;
    font-size: 1.1rem;
  }

  .btn-primary,
  .btn-secondary {
    min-height: 52px;
    padding: 16px 24px;
    font-size: 1.1rem;
  }

  .service-option,
  .urgency-option {
    padding: 0.8rem;
    font-size: 0.95rem;
  }
}
