.services-section {
  max-width: 1200px;
  margin: 3rem auto;
  padding: 0 1.5rem;
}

.services-section h2 {
  color: #3a86ff;
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
}

.services-section h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #3a86ff, transparent);
}

.services-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 2rem;
  color: #cbd5e1;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Search and Filter Styles */
.services-search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.search-input-container {
  position: relative;
  flex: 1;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 1rem;
}

.services-search-input {
  width: 100%;
  padding: 0.8rem 1rem 0.8rem 2.5rem;
  border-radius: 8px;
  border: 1px solid rgba(58, 134, 255, 0.3);
  background: rgba(15, 23, 42, 0.5);
  color: #e2e8f0;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.services-search-input:focus {
  outline: none;
  border-color: #3a86ff;
  box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.2);
}

.services-search-input::placeholder {
  color: #94a3b8;
}

.clear-search {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.clear-search:hover {
  color: #e2e8f0;
}

.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 8px;
  padding: 0.8rem 1.2rem;
  color: #e2e8f0;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-toggle-btn:hover,
.filter-toggle-btn.active {
  background: rgba(58, 134, 255, 0.1);
  border-color: rgba(58, 134, 255, 0.5);
}

.filter-toggle-btn.active {
  color: #3a86ff;
}

.filters-panel {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  overflow: hidden;
}

.filter-group {
  margin-bottom: 1.5rem;
}

.filter-group:last-child {
  margin-bottom: 1rem;
}

.filter-group h4 {
  color: #e2e8f0;
  font-size: 1rem;
  margin: 0 0 0.8rem 0;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-option {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 6px;
  padding: 0.5rem 1rem;
  color: #cbd5e1;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-option:hover {
  background: rgba(58, 134, 255, 0.1);
  border-color: rgba(58, 134, 255, 0.4);
}

.filter-option.active {
  background: rgba(58, 134, 255, 0.15);
  border-color: rgba(58, 134, 255, 0.5);
  color: #3a86ff;
  font-weight: 500;
}

.reset-filters-btn {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(203, 213, 225, 0.3);
  border-radius: 6px;
  padding: 0.6rem 1rem;
  color: #cbd5e1;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-filters-btn:hover {
  background: rgba(15, 23, 42, 0.7);
  color: #e2e8f0;
  border-color: rgba(203, 213, 225, 0.5);
}

.search-results-summary {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 0.8rem 1.2rem;
  color: #cbd5e1;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.search-results-summary span {
  color: #3a86ff;
}

.clear-filters-btn {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 0.85rem;
  cursor: pointer;
  padding: 0.3rem 0.6rem;
  margin-left: auto;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background: rgba(58, 134, 255, 0.1);
  color: #e2e8f0;
}

.no-services-found {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.no-services-found h3 {
  color: #e2e8f0;
  font-size: 1.3rem;
  margin: 0 0 1rem 0;
}

.no-services-found p {
  color: #cbd5e1;
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.reset-search-btn {
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 6px;
  padding: 0.7rem 1.5rem;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-search-btn:hover {
  background: rgba(58, 134, 255, 0.2);
  border-color: rgba(58, 134, 255, 0.5);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.service-category {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.service-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(58, 134, 255, 0.6);
}

.service-category.expanded {
  box-shadow: 0 15px 30px rgba(58, 134, 255, 0.2);
  border-color: rgba(58, 134, 255, 0.6);
}

.service-header {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  cursor: pointer;
  position: relative;
  transition: background-color 0.3s ease;
}

.service-header:hover {
  background-color: rgba(58, 134, 255, 0.1);
}

.service-icon {
  color: #3a86ff;
  font-size: 1.5rem;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(58, 134, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.service-category:hover .service-icon,
.service-category.expanded .service-icon {
  transform: scale(1.1);
  background: rgba(58, 134, 255, 0.2);
}

.service-header h3 {
  color: #e2e8f0;
  font-size: 1.2rem;
  margin: 0;
  flex-grow: 1;
}

.toggle-icon {
  color: #3a86ff;
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.service-category.expanded .toggle-icon {
  transform: rotate(180deg);
}

.service-details {
  padding: 0 1.2rem 1.2rem;
  overflow: hidden;
}

.category-description {
  color: #cbd5e1;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.8rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(58, 134, 255, 0.2);
}

.service-packages {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 0.8rem;
  max-height: 280px;
  overflow-y: auto;
  padding-right: 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(58, 134, 255, 0.5) rgba(15, 23, 42, 0.3);
}

.service-packages::-webkit-scrollbar {
  width: 6px;
}

.service-packages::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

.service-packages::-webkit-scrollbar-thumb {
  background-color: rgba(58, 134, 255, 0.5);
  border-radius: 4px;
}

.service-package {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 0.7rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-package:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: rgba(58, 134, 255, 0.4);
}

.service-package::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #3a86ff, rgba(58, 134, 255, 0.2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-package:hover::before {
  opacity: 1;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(58, 134, 255, 0.1);
}

.package-header h4 {
  color: #3a86ff;
  font-size: 1rem;
  margin: 0;
  transition: transform 0.3s ease;
}

.service-package:hover .package-header h4 {
  transform: translateX(3px);
}

.package-price {
  color: #10b981;
  font-weight: 600;
  font-size: 0.9rem;
  background: rgba(16, 185, 129, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
  border: 1px solid rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}

.service-package:hover .package-price {
  background: rgba(16, 185, 129, 0.2);
  transform: scale(1.05);
}

.package-description {
  color: #cbd5e1;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.6rem;
  transition: color 0.3s ease;
}

.service-package:hover .package-description {
  color: #e2e8f0;
}

.package-features {
  margin-bottom: 0.6rem;
}

.package-features h5 {
  color: #e2e8f0;
  font-size: 0.85rem;
  margin: 0 0 0.3rem 0;
  display: inline-block;
  margin-right: 0.5rem;
}

.package-features ul {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.package-features li {
  color: #cbd5e1;
  font-size: 0.8rem;
  padding: 0.15rem 0;
  padding-left: 1.1rem;
  position: relative;
}

.package-features li::before {
  content: '✓';
  color: #10b981;
  position: absolute;
  left: 0;
  top: 0.15rem;
  transition: transform 0.2s ease;
}

.service-package:hover .package-features li::before {
  transform: scale(1.1);
}

.package-features li.hidden-feature {
  display: none;
}

.package-features li.more-features,
.package-features li.less-features {
  color: #3a86ff;
  font-style: italic;
  font-size: 0.75rem;
  cursor: pointer;
  text-decoration: underline;
  transition: all 0.2s ease;
}

.package-features li.more-features:hover,
.package-features li.less-features:hover {
  color: #5a9aff;
  transform: translateX(2px);
}

.package-features li.more-features::before,
.package-features li.less-features::before {
  content: '•';
  color: #3a86ff;
}

.package-timeline {
  color: #cbd5e1;
  font-size: 0.8rem;
  margin-top: 0.4rem;
  padding-top: 0.4rem;
  border-top: 1px solid rgba(58, 134, 255, 0.1);
}

.package-timeline span {
  font-weight: 600;
  color: #e2e8f0;
}

.book-service-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 4px;
  padding: 0.5rem 0.8rem;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.8rem;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.book-service-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(58, 134, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: translate(-50%, -50%) scale(1);
  transition: all 0.5s ease;
}

.book-service-btn:hover::after {
  opacity: 1;
  transform: translate(-50%, -50%) scale(50);
  background: rgba(58, 134, 255, 0.2);
}

.book-service-btn:hover {
  background: rgba(58, 134, 255, 0.1);
  border-color: rgba(58, 134, 255, 0.5);
  transform: translateY(-2px);
  color: #ffffff;
}

/* Responsive styles */
@media (max-width: 768px) {
  .services-section {
    padding: 0 1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-packages {
    grid-template-columns: 1fr;
    max-height: 500px; /* Taller on mobile for better scrolling */
  }

  .package-features li {
    flex: 0 0 100%; /* Full width on mobile */
  }

  .service-header {
    padding: 1.2rem;
    min-height: 44px;
    cursor: pointer;
  }

  .service-icon {
    font-size: 1.2rem;
    width: 44px; /* Touch-friendly size */
    height: 44px;
    min-width: 44px;
  }

  .service-header h3 {
    font-size: 1.1rem;
  }

  .service-details {
    padding: 0 1.2rem 1.2rem;
  }

  .package-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .package-price {
    margin-top: 0.5rem;
  }

  /* Responsive search and filter styles */
  .services-search-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-input-container {
    max-width: 100%;
  }

  .search-input {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 1rem;
  }

  .filter-toggle-btn {
    align-self: flex-end;
    min-height: 44px;
    padding: 12px 16px;
  }

  .filter-options {
    flex-direction: column;
    gap: 0.7rem;
  }

  .filter-option {
    width: 100%;
    text-align: center;
    min-height: 44px;
    padding: 12px;
  }

  .search-results-summary {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }

  .clear-filters-btn {
    margin-left: 0;
    margin-top: 0.5rem;
    align-self: flex-end;
    min-height: 44px;
    padding: 12px 16px;
  }

  .package-card {
    padding: 1.2rem;
  }

  .package-cta {
    min-height: 48px;
    padding: 12px 24px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .services-section {
    padding: 0 0.75rem;
  }

  .service-header {
    padding: 1rem;
  }

  .service-icon {
    width: 48px;
    height: 48px;
    min-width: 48px;
  }

  .search-input {
    min-height: 46px;
    padding: 14px;
  }

  .filter-toggle-btn,
  .filter-option,
  .clear-filters-btn {
    min-height: 46px;
    padding: 14px;
  }

  .package-cta {
    min-height: 50px;
    padding: 14px 24px;
  }
}

@media (max-width: 375px) {
  .services-section {
    padding: 0 0.5rem;
  }

  .service-header {
    padding: 0.8rem;
  }

  .service-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
  }

  .search-input {
    min-height: 48px;
    padding: 16px;
    font-size: 1.1rem;
  }

  .filter-toggle-btn,
  .filter-option,
  .clear-filters-btn {
    min-height: 48px;
    padding: 16px;
    font-size: 1rem;
  }

  .package-cta {
    min-height: 52px;
    padding: 16px 24px;
    font-size: 1.1rem;
  }

  .service-header h3 {
    font-size: 1rem;
  }
}
