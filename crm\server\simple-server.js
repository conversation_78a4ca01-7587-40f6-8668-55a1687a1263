#!/usr/bin/env node

/**
 * Simple CRM Server for Testing
 * Minimal server configuration to test core functionality
 */

const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import database connection
const { testConnection } = require('../config/database');

// Import routes
const authRoutes = require('./routes/auth');
const formRoutes = require('./routes/forms');

const app = express();
const PORT = process.env.PORT || 3001;

// Basic middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbConnected = await testConnection();
    res.status(dbConnected ? 200 : 503).json({
      status: dbConnected ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: dbConnected ? 'connected' : 'disconnected',
      version: '1.0.0',
      server: 'simple-crm-server'
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'error',
      error: error.message
    });
  }
});

// Basic info endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'CRM API Server',
    version: '1.0.0',
    endpoints: [
      'GET /health - Health check',
      'POST /api/auth/login - User login',
      'POST /api/auth/register - User registration',
      'POST /api/forms/submit - Form submission',
      'GET /api/forms - List form submissions (auth required)'
    ]
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/forms', formRoutes);

// Test endpoint for AI integration
app.post('/api/test/ai', async (req, res) => {
  try {
    const aiService = require('./services/aiService');
    const { formType, customerData, submissionData } = req.body;
    
    const response = await aiService.generateEmailResponse(
      formType || 'test',
      customerData || { first_name: 'Test', email: '<EMAIL>' },
      submissionData || { message: 'Test message' }
    );
    
    res.json({
      success: true,
      ai_response: response
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'AI test failed',
      error: error.message
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl,
    available_endpoints: ['/health', '/api/auth/*', '/api/forms/*', '/api/test/ai']
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
async function startServer() {
  try {
    console.log('🚀 Starting Simple CRM Server...');
    
    // Test database connection
    console.log('📊 Testing database connection...');
    const dbConnected = await testConnection();
    if (dbConnected) {
      console.log('✅ Database connected successfully');
    } else {
      console.log('⚠️ Database connection failed, but continuing...');
    }
    
    // Start HTTP server
    const server = app.listen(PORT, () => {
      console.log(`🌐 Simple CRM Server running on port ${PORT}`);
      console.log(`📋 Available endpoints:`);
      console.log(`   GET  http://localhost:${PORT}/health`);
      console.log(`   POST http://localhost:${PORT}/api/auth/login`);
      console.log(`   POST http://localhost:${PORT}/api/forms/submit`);
      console.log(`   POST http://localhost:${PORT}/api/test/ai`);
      console.log(`\n🧪 Test the server:`);
      console.log(`   curl http://localhost:${PORT}/health`);
      console.log(`\n🎯 Ready for testing!`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    return server;
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start server if called directly
if (require.main === module) {
  startServer().catch(error => {
    console.error('Server startup failed:', error);
    process.exit(1);
  });
}

module.exports = { app, startServer };
