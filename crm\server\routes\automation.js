const express = require('express');
const { query } = require('../../config/database');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * Get automation rules
 * GET /api/automation/rules
 */
router.get('/rules', requireAdmin, async (req, res) => {
  try {
    const rules = await query(`
      SELECT * FROM automation_rules 
      ORDER BY created_at DESC
    `);

    res.json({
      success: true,
      data: rules.rows
    });
  } catch (error) {
    console.error('Get automation rules error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve automation rules'
    });
  }
});

/**
 * Create automation rule
 * POST /api/automation/rules
 */
router.post('/rules', requireAdmin, async (req, res) => {
  try {
    const { name, triggerType, triggerConditions, actions } = req.body;
    const userId = req.user.id;

    if (!name || !triggerType || !triggerConditions || !actions) {
      return res.status(400).json({
        success: false,
        message: 'Name, trigger type, conditions, and actions are required'
      });
    }

    const result = await query(`
      INSERT INTO automation_rules (name, trigger_type, trigger_conditions, actions, created_by)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [
      name,
      triggerType,
      JSON.stringify(triggerConditions),
      JSON.stringify(actions),
      userId
    ]);

    res.status(201).json({
      success: true,
      message: 'Automation rule created successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Create automation rule error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create automation rule'
    });
  }
});

/**
 * Update automation rule
 * PUT /api/automation/rules/:id
 */
router.put('/rules/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, triggerType, triggerConditions, actions, isActive } = req.body;

    const result = await query(`
      UPDATE automation_rules 
      SET name = $1, trigger_type = $2, trigger_conditions = $3, actions = $4, is_active = $5
      WHERE id = $6
      RETURNING *
    `, [
      name,
      triggerType,
      JSON.stringify(triggerConditions),
      JSON.stringify(actions),
      isActive,
      id
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Automation rule not found'
      });
    }

    res.json({
      success: true,
      message: 'Automation rule updated successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Update automation rule error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update automation rule'
    });
  }
});

/**
 * Delete automation rule
 * DELETE /api/automation/rules/:id
 */
router.delete('/rules/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      'DELETE FROM automation_rules WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Automation rule not found'
      });
    }

    res.json({
      success: true,
      message: 'Automation rule deleted successfully'
    });
  } catch (error) {
    console.error('Delete automation rule error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete automation rule'
    });
  }
});

module.exports = router;
