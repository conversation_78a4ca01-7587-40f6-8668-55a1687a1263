/* ========================================
   🌐 GLOBAL RESETS & TYPOGRAPHY
======================================== */
:root {
  /* Main colors */
  --primary-color: #3a86ff;
  --secondary-color: #8338ec;
  --accent-color: #ff006e;
  --text-color: #e2e8f0;
  --bg-color: #0f172a;
  --bg-accent-color: #1e293b;

  /* Blockchain line settings */
  --line-opacity: 0.8;
  --line-width: 3px;
  --line-color: #3a86ff;
  --line-accent-color: #f7931a;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  background-color: var(--bg-color); /* Use CSS variable for background color */
  color: #fdfdfd;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
  /* Improve touch scrolling on mobile */
  -webkit-overflow-scrolling: touch;
  /* Prevent text selection on touch devices for better UX */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for content areas */
p, span, div[contenteditable], input, textarea, .selectable-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

html, #root {
  margin: 0;
  padding: 0;
  background: transparent;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* App wrapper to ensure content is above canvas */
.app-wrapper {
  position: relative;
  z-index: 1; /* Higher than canvas */
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
  padding-top: 80px; /* Add padding to account for fixed tickers at top */
}

/* Media query for mobile devices */
@media (max-width: 768px) {
  .app-wrapper {
    padding-top: 90px; /* Slightly more padding on mobile */
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Responsive typography for mobile */
  .typing-hello-world {
    font-size: 2.2rem; /* Smaller on mobile */
  }

  .typing-dj-martin {
    font-size: 1.8rem; /* Smaller on mobile */
  }

  .hero-header h1 {
    font-size: 2.2rem; /* Smaller on mobile */
  }

  .hero-header h2,
  .hero-header h3,
  .hero-header p {
    font-size: 1rem; /* Smaller on mobile */
  }
}

/* Additional mobile breakpoints for better responsiveness */
@media (max-width: 480px) {
  .app-wrapper {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .typing-hello-world {
    font-size: 1.8rem;
  }

  .typing-dj-martin {
    font-size: 1.5rem;
  }

  .hero-header h1 {
    font-size: 1.8rem;
  }
}

@media (max-width: 375px) {
  .app-wrapper {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .typing-hello-world {
    font-size: 1.6rem;
  }

  .typing-dj-martin {
    font-size: 1.3rem;
  }

  .hero-header h1 {
    font-size: 1.6rem;
  }
}

@media (max-width: 320px) {
  .app-wrapper {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .typing-hello-world {
    font-size: 1.4rem;
  }

  .typing-dj-martin {
    font-size: 1.2rem;
  }

  .hero-header h1 {
    font-size: 1.4rem;
  }

  /* Ensure all interactive elements are touch-friendly on very small screens */
  button, .btn, .button {
    min-height: 48px;
    min-width: 48px;
    padding: 14px 18px;
    font-size: 1.1rem;
  }

  input, select, textarea {
    min-height: 48px;
    padding: 14px 18px;
    font-size: 1.1rem;
  }

  /* Adjust interface container for very small screens */
  .interface-container {
    padding: 1rem 0.5rem;
    gap: 1rem;
  }

  .repo-panel,
  .function-panel {
    padding: 0.75rem;
    min-height: auto;
  }
}
.crypto-ticker {
  margin-top: 0;
  padding-top: 0;
}

h1, h2, h3 {
  font-family: 'Poppins', sans-serif;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

a {
  color: #00fff7;
  text-decoration: none;
  /* Improve touch targets */
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  padding: 8px;
  margin: -8px;
}
a:hover {
  text-decoration: underline;
}

/* Global touch-friendly button improvements */
button, .btn, .button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  /* Improve touch feedback */
  -webkit-tap-highlight-color: rgba(58, 134, 255, 0.3);
  touch-action: manipulation;
}

/* Global form element improvements */
input, select, textarea {
  min-height: 44px;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.3);
  background-color: rgba(15, 23, 42, 0.5);
  color: #e2e8f0;
  font-size: 1rem;
  transition: all 0.2s ease;
  /* Improve mobile input experience */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3a86ff;
  box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.2);
}

/* ========================================
   🌊 BACKGROUND DECORATION
======================================== */
.wave-bg {
  background: url('https://www.svgrepo.com/show/349580/wave.svg') repeat-x bottom;
  position: absolute;
  width: 100%;
  height: 100px;
  bottom: 0;
  left: 0;
  z-index: -1;
  opacity: 0.2;
}

/* App wrapper styles are defined at the top of the file */


/* ========================================
   📦 LAYOUT STRUCTURE
======================================== */
.interface-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding: 3rem 2rem;
  justify-content: center;
}

/* ========================================
   🧱 PANEL WRAPPERS (Projects, Functions)
======================================== */
.repo-panel,
.function-panel {
  background: rgba(15, 23, 42, 0.8);
  border: 2px solid rgba(58, 134, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 25px rgba(58, 134, 255, 0.15);
  flex: 1 1 200px;
  min-width: 300px;
  min-height: 590px;
  max-height: 550px;
  overflow-y: auto;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  z-index: 15;
}

.repo-panel:hover,
.function-panel:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(58, 134, 255, 0.25);
}

.repo-panel h2,
.function-panel h2 {
  color: #3a86ff;
  font-size: 1.4rem;
  text-shadow: 0 0 5px rgba(58, 134, 255, 0.3);
  margin-bottom: 1rem;
  border-bottom: 2px solid rgba(58, 134, 255, 0.2);
  padding-bottom: 0.5rem;
}

/* ===== Scrollbar Styling ===== */
.repo-panel::-webkit-scrollbar,
.function-panel::-webkit-scrollbar,
.info-feed::-webkit-scrollbar,
.big-modal::-webkit-scrollbar {
  width: 6px;
}

.repo-panel::-webkit-scrollbar-thumb,
.function-panel::-webkit-scrollbar-thumb,
.info-feed::-webkit-scrollbar-thumb,
.big-modal::-webkit-scrollbar-thumb {
  background-color: #3a86ff;
  border-radius: 4px;
}

.repo-panel::-webkit-scrollbar-track,
.function-panel::-webkit-scrollbar-track,
.info-feed::-webkit-scrollbar-track,
.big-modal::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

/* ========================================
   🚀 PROJECT CARDS (Repos)
======================================== */
.card {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  color: #fff;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #3a86ff;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px) scale(1.01);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(58, 134, 255, 0.6);
}

.card:hover::before {
  width: 100%;
  opacity: 0.1;
}

/* Ensure the card doesn't block pointer events for the button */
.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 5;
  pointer-events: none;
}

.card h3 {
  margin-top: 0;
  color: #3a86ff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.card p {
  color: #cbd5e1;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1; /* Allow paragraph to take available space */
}

.card a {
  display: inline-block;
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  border: 1px solid rgba(58, 134, 255, 0.3);
  position: relative;
  z-index: 10; /* Ensure button is above other elements */
  cursor: pointer;
}

.card a:hover {
  background: rgba(58, 134, 255, 0.2);
  transform: translateY(-2px);
  text-decoration: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card a:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ========================================
   🌟 PORTFOLIO HUB BUTTONS
======================================== */
.function-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 2rem;
}

.function-grid button {
  flex: 1 1 45%;
  background-color: #3a86ff;
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  padding: 1rem 1.25rem;
  margin: 0.4rem 0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px; /* Ensure minimum touch target size */
  font-size: 1rem;
}

.function-grid button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.function-grid button:hover {
  background-color: #1d65d8;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.function-grid button:hover::before {
  left: 100%;
}

.function-grid button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ========================================
   📰 TECH NEWS FEED (Enhanced Bullets)
======================================== */
.info-feed {
  margin-top: 1.5rem;
  padding: 1.5rem;
  border-radius: 12px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(58, 134, 255, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
}

.info-feed h3 {
  color: #3a86ff;
  font-size: 1.3rem;
  margin-bottom: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(58, 134, 255, 0.2);
}

.info-feed ul {
  list-style-type: none;
  padding-left: 0.5rem;
}

.info-feed li {
  margin-bottom: 1.2rem;
  font-size: 0.95rem;
  line-height: 1.4;
  position: relative;
  padding-left: 1.5rem;
}

.info-feed li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: #3a86ff;
  font-weight: bold;
}

.info-feed a {
  color: #cbd5e1;
  transition: all 0.2s ease;
  text-decoration: none;
  border-bottom: 1px dashed rgba(58, 134, 255, 0.3);
  padding-bottom: 2px;
}

.info-feed a:hover {
  color: #3a86ff;
  border-bottom: 1px solid #3a86ff;
  text-decoration: none;
}

/* ========================================
   🧬 HERO HEADER
======================================== */
.hero-header {
  text-align: center;
  margin-top: 0rem;
  padding: 3rem 0 2rem;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.hero-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #3a86ff, transparent);
}

.hero-header h1,
.hero-header h2,
.hero-header h3,
.hero-header p {
  margin: 0;
  line-height: 1.4;
  padding: 0;
}

.hero-header h1 {
  font-size: 3rem;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, #3a86ff, #8338ec);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(58, 134, 255, 0.3);
}

.hero-header h2,
.hero-header h3,
.hero-header p {
  font-size: 1.2rem;
  color: #cbd5e1;
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
}

.hero-header p {
  color: #3a86ff !important;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* Typing Effect Styles */
.typing-effect {
  display: inline-block;
}

.typing-effect .cursor {
  display: inline-block;
  margin-left: 2px;
  color: #3a86ff;
  font-weight: bold;
}

.typing-hello-world {
  font-size: 3rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
}

.typing-hello-world .typed-text {
  background: linear-gradient(90deg, #3a86ff, #8338ec);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(58, 134, 255, 0.3);
}

.typing-dj-martin {
  font-size: 2.5rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

.typing-dj-martin .typed-text {
  background: linear-gradient(90deg, #3a86ff, #8338ec);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ========================================
   📄 MODAL STYLES
======================================== */
.big-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  background: #111;
  color: #fff;
  border: 3px solid #3a86ff;
  border-radius: 12px;
  transform: translate(-50%, -50%);
  padding: 2rem;
  z-index: 999;
  box-shadow: 0 0 30px rgba(58, 134, 255, 0.3);
  scrollbar-width: thin;
  scrollbar-color: rgba(58, 134, 255, 0.6) rgba(15, 23, 42, 0.3);
}

/* Mobile modal improvements */
@media (max-width: 768px) {
  .big-modal {
    width: 95%;
    max-height: 90vh;
    padding: 1.5rem;
    border-radius: 8px;
    border-width: 2px;
  }
}

@media (max-width: 480px) {
  .big-modal {
    width: 98%;
    max-height: 95vh;
    padding: 1rem;
    border-radius: 6px;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

@media (max-width: 375px) {
  .big-modal {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    border: none;
    top: 0;
    left: 0;
    transform: none;
    padding: 1rem;
  }
}

/* Custom scrollbar for Webkit browsers */
.big-modal::-webkit-scrollbar {
  width: 6px;
}

.big-modal::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 10px;
}

.big-modal::-webkit-scrollbar-thumb {
  background: rgba(58, 134, 255, 0.6);
  border-radius: 10px;
}

.big-modal::-webkit-scrollbar-thumb:hover {
  background: rgba(58, 134, 255, 0.8);
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 998;
}

.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #3a86ff;
  color: white;
  font-size: 1.2rem;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #1d65d8;
  transform: scale(1.1);
}

/* PDF Viewer Styling */
.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem 0;
}

.react-pdf__Page {
  margin: 1rem 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  overflow: hidden;
}

.react-pdf__Page__canvas {
  display: block;
  max-width: 100%;
  height: auto !important;
}

.pdf-loading,
.pdf-error {
  padding: 2rem;
  text-align: center;
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 8px;
  margin: 2rem 0;
  width: 100%;
  max-width: 600px;
}

.pdf-loading {
  color: #3a86ff;
  font-size: 1.2rem;
  animation: pulse 1.5s infinite;
}

.pdf-error {
  color: #ef4444;
  font-size: 1.2rem;
}

/* PDF Navigation */
nav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
}

nav button {
  background: #3a86ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

nav button:hover:not(:disabled) {
  background: #1d65d8;
  transform: scale(1.05);
}

.pdf-fallback-link {
  display: block;
  margin-top: 15px;
  text-align: center;
  color: #3a86ff;
  text-decoration: underline;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.pdf-fallback-link:hover {
  color: #1d65d8;
}

nav button:disabled {
  background: #666;
  cursor: not-allowed;
  opacity: 0.7;
}

nav p {
  margin: 0;
  font-size: 0.9rem;
  color: #ccc;
}

/* ========================================
   📁 PROJECTS MODAL STYLES
======================================== */
.projects-container {
  max-width: 900px;
  margin: 0 auto;
  max-height: 75vh;
  overflow-y: auto;
}

.projects-intro {
  text-align: center;
  margin-bottom: 2rem;
  color: #cbd5e1;
  font-size: 1.1rem;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.project-card {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 10px;
  padding: 1rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #3a86ff;
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(58, 134, 255, 0.6);
}

.project-card:hover::before {
  width: 100%;
  opacity: 0.1;
}

.project-card h3 {
  color: #3a86ff;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.project-card p {
  color: #cbd5e1;
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.8rem;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.6rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.project-language {
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  padding: 0.3rem 0.6rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.project-links {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.project-link {
  display: inline-block;
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  padding: 0.4rem 0.7rem;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  border: 1px solid rgba(58, 134, 255, 0.3);
  font-size: 0.8rem;
  text-align: center;
  flex: 1;
}

.project-link:hover {
  background: rgba(58, 134, 255, 0.2);
  transform: translateY(-2px);
  text-decoration: none;
}

.demo-link {
  background: rgba(79, 70, 229, 0.1);
  color: #8b5cf6;
  border-color: rgba(139, 92, 246, 0.3);
}

.demo-link:hover {
  background: rgba(79, 70, 229, 0.2);
}

@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }
}

/* ========================================
   📝 BLOG SECTION STYLES
======================================== */
.blog-section {
  max-width: 1200px;
  margin: 3rem auto;
  padding: 0 1.5rem;
}

.section-header {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-bottom: 1.5rem;
  position: relative;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(15, 23, 42, 0.5);
  border: 1px dashed rgba(58, 134, 255, 0.3);
  transition: all 0.3s ease;
}

.section-header:hover {
  background: rgba(15, 23, 42, 0.7);
  border-color: rgba(58, 134, 255, 0.5);
}

.section-header h2 {
  color: #3a86ff;
  font-size: 1.8rem;
  margin: 0;
  position: relative;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #3a86ff, transparent);
}

.section-header .toggle-icon {
  color: #3a86ff;
  font-size: 1.2rem;
  margin-left: 1rem;
  transition: transform 0.3s ease;
}

.section-header:hover .toggle-icon {
  transform: scale(1.2);
}

@media (max-width: 768px) {
  .blog-section {
    margin: 2rem auto;
    padding: 0 1rem;
  }
}

/* ========================================
   🛠️ SKILLS STYLES
======================================== */
.skills-container {
  max-width: 800px;
  margin: 0 auto;
}

.skills-intro {
  text-align: center;
  margin-bottom: 2rem;
  color: #cbd5e1;
  font-size: 1.1rem;
}

.skills-grid {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin-bottom: 2.5rem;
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skill-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 1rem;
}

.skill-bar {
  height: 10px;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 5px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  border-radius: 5px;
  transition: width 1s ease-in-out;
}

.additional-skills {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(58, 134, 255, 0.2);
}

.additional-skills h3 {
  color: #3a86ff;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  text-align: left;
  display: flex;
  align-items: center;
}

.additional-skills h3::after {
  content: '';
  flex-grow: 1;
  height: 1px;
  background: linear-gradient(90deg, rgba(58, 134, 255, 0.5), transparent);
  margin-left: 10px;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  justify-content: flex-start;
  margin-bottom: 1.5rem;
}

.skill-tag {
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  border: 1px solid rgba(58, 134, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.skill-tag:hover {
  background: rgba(58, 134, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.highlight-skill {
  background: rgba(58, 134, 255, 0.25);
  border: 1px solid rgba(58, 134, 255, 0.5);
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(58, 134, 255, 0.2);
}

/* ========================================
   📬 CONTACT FORM STYLES
======================================== */
.contact-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.contact-intro {
  text-align: center;
  margin-bottom: 2rem;
  color: #cbd5e1;
  font-size: 1.1rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-success {
  background-color: rgba(72, 187, 120, 0.2);
  color: #48bb78;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 500;
}

.form-error {
  background-color: rgba(245, 101, 101, 0.2);
  color: #f56565;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 500;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #94a3b8;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(58, 134, 255, 0.3);
  background: rgba(15, 23, 42, 0.6);
  color: white;
  font-family: inherit;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3a86ff;
  box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #64748b;
}

.submit-btn {
  background: #3a86ff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submit-btn:hover:not(:disabled) {
  background: #1d65d8;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.submit-btn:disabled {
  background: #6c7a93;
  cursor: not-allowed;
  opacity: 0.7;
}

.contact-direct {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(58, 134, 255, 0.2);
}

.contact-direct p {
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.email-link {
  color: #3a86ff;
  font-weight: 500;
  text-decoration: none;
  border-bottom: 1px dashed rgba(58, 134, 255, 0.5);
  padding-bottom: 2px;
  transition: all 0.2s ease;
}

.email-link:hover {
  border-bottom: 1px solid #3a86ff;
}

/* ========================================
   📬 FOOTER STYLES
======================================== */
footer {
  text-align: center;
  padding: 2.5rem 1rem;
  background: rgba(15, 23, 42, 0.8);
  font-size: 0.9rem;
  margin-top: 3rem;
  border-top: 1px solid rgba(58, 134, 255, 0.2);
  position: relative;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(58, 134, 255, 0.5), transparent);
}

footer .socials {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

footer .socials a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  transition: all 0.3s ease;
}

footer .socials a:hover {
  background: #3a86ff;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(58, 134, 255, 0.3);
}

footer .footer-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

footer .footer-links a {
  color: #3a86ff;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
  opacity: 0.8;
}

footer .footer-links a:hover {
  opacity: 1;
  text-decoration: underline;
  transform: translateY(-2px);
}

footer p {
  color: #94a3b8;
  margin-top: 1rem;
}

.secret-hint {
  font-size: 0.75rem !important;
  opacity: 0.5;
  margin-top: 5px !important;
  cursor: default;
  color: #3a86ff !important;
  position: relative;
  transition: opacity 0.3s ease;
}

.secret-hint:hover {
  opacity: 0.8;
}

/* Add a subtle pulse animation when the user types any key */
@keyframes secretPulse {
  0% { opacity: 0.5; }
  50% { opacity: 0.9; }
  100% { opacity: 0.5; }
}

.typing .secret-hint {
  animation: secretPulse 1.5s infinite;
}

/* Secret notification styling */
.secret-notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: linear-gradient(135deg, #f7931a 0%, #f7931a 50%, #2775ca 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 2000;
  opacity: 0;
  transform: translateY(20px);
  animation: notificationFadeIn 0.5s forwards;
}

@keyframes notificationFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.secret-notification.fade-out {
  animation: notificationFadeOut 0.5s forwards;
}

@keyframes notificationFadeOut {
  to {
    opacity: 0;
    transform: translateY(20px);
  }
}

/* ========================================
   🧠 BLOG + AI PANELS
======================================== */
.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding: 1.5rem 0;
}
.ai-box, .blog-box {
  flex: 1;
  min-width: 300px;
}
.ai-box { max-width: 50%; }
.blog-box { max-width: 50%; }

/* ========================================
   📱 RESPONSIVE MEDIA QUERIES
======================================== */
@media (max-width: 768px) {
  .function-grid button {
    flex: 1 1 100%;
    min-height: 48px; /* Larger touch targets on mobile */
    padding: 1.2rem 1.5rem;
    font-size: 1.1rem;
    margin: 0.5rem 0;
  }

  .ai-box, .blog-box {
    max-width: 100%;
  }

  .repo-panel,
  .function-panel {
    min-width: 100%;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .function-grid button {
    min-height: 50px;
    padding: 1.3rem 1.5rem;
    font-size: 1rem;
    margin: 0.6rem 0;
  }

  .repo-panel,
  .function-panel {
    padding: 1rem;
    border-radius: 12px;
  }
}

@media (max-width: 375px) {
  .function-grid button {
    min-height: 52px;
    padding: 1.4rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .blog-grid {
    grid-template-columns: 1fr;
  }
}

/* === Blog Feed Grid === */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

/* === Blog Card Styling === */
.blog-card {
  display: block;
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.blog-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: #3a86ff;
  transition: all 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(58, 134, 255, 0.6);
}

.blog-card:hover::before {
  width: 100%;
  opacity: 0.1;
}

.blog-card h3 {
  margin: 0 0 0.8rem 0;
  color: #3a86ff;
  font-size: 1.2rem;
  font-weight: 600;
}

.blog-card p {
  color: #cbd5e1;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.blog-description {
  color: #cbd5e1;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.5;
}

.blog-card small {
  display: block;
  margin-bottom: 0.8rem;
  color: #94a3b8;
  font-size: 0.8rem;
}

.blog-card a {
  display: inline-block;
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  border: 1px solid rgba(58, 134, 255, 0.3);
  margin-top: 0.5rem;
}

.blog-card a:hover {
  background: rgba(58, 134, 255, 0.2);
  transform: translateY(-2px);
  text-decoration: none;
}

.meta {
  font-size: 0.8rem;
  color: #94a3b8;
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  border-top: 1px solid rgba(58, 134, 255, 0.1);
  padding-top: 0.8rem;
}

