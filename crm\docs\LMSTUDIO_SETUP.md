# LM Studio Integration Guide for Portfolio CRM

This guide provides step-by-step instructions for integrating LM Studio with your Portfolio CRM system for GPU-accelerated AI email automation.

## 🎯 Overview

LM Studio provides local GPU-powered language model inference with an OpenAI-compatible API. This integration allows your CRM to generate personalized email responses using your local hardware, ensuring privacy and eliminating API costs.

## 📋 Prerequisites

- **NVIDIA GPU** with CUDA support (8GB+ VRAM recommended)
- **Windows 10/11** or **macOS** or **Linux**
- **16GB+ RAM** (32GB recommended for larger models)
- **LM Studio** application installed

## 🚀 Quick Setup (5 Minutes)

### Step 1: Install LM Studio
1. Download LM Studio from [https://lmstudio.ai/](https://lmstudio.ai/)
2. Install the application for your operating system
3. Launch LM Studio

### Step 2: Download Phi-3.1 Mini Instruct Model
1. In LM Studio, click on the **"Discover"** tab
2. Search for: `microsoft/Phi-3.1-mini-4K-Instruct`
3. Look for a **4-bit quantized version** (e.g., `Phi-3.1-mini-4K-Instruct-Q4_K_M.gguf`)
4. Click **"Download"** and wait for completion

### Step 3: Load the Model
1. Go to the **"Chat"** tab
2. Click **"Select a model to load"**
3. Choose the downloaded `Phi-3.1-mini-4K-Instruct` model
4. Wait for the model to load (you'll see GPU utilization)

### Step 4: Start the Local Server
1. Navigate to the **"Local Server"** tab
2. Select your loaded model from the dropdown
3. Configure server settings:
   - **Port**: `1234` (default)
   - **CORS**: ✅ Enable
   - **API Key**: Leave empty (not needed for local use)
4. Click **"Start Server"**

### Step 5: Verify Setup
```bash
# Test the API endpoint
curl http://localhost:1234/v1/models

# Expected response:
{
  "object": "list",
  "data": [
    {
      "id": "phi3.1-mini-instruct",
      "object": "model",
      "created": **********,
      "owned_by": "lmstudio"
    }
  ]
}
```

### Step 6: Configure CRM Environment
Create or update your `.env` file in the CRM directory:

```env
# AI Configuration
AI_PROVIDER=lmstudio
LMSTUDIO_URL=http://localhost:1234
LMSTUDIO_MODEL=phi3.1-mini-instruct

# Optional: Fallback configuration
# AI_FALLBACK_PROVIDER=ollama
```

### Step 7: Run Health Check
```bash
cd crm
npm install
npm run check-lmstudio
```

Expected output:
```
🔍 Starting LM Studio Health Check...

1️⃣ Checking if LM Studio server is running...
✅ Server is running

2️⃣ Checking available models...
✅ Found 1 model(s):
   - phi3.1-mini-instruct

3️⃣ Testing API responsiveness...
✅ API is responsive

4️⃣ Checking for phi3.1-mini-instruct model...
✅ Phi-3.1 Mini model found: phi3.1-mini-instruct

5️⃣ Testing text generation...
   Generated text: "Hello, CRM system! I'm ready to assist with professional email responses."
✅ Text generation successful

🎉 All checks passed! LM Studio is ready for CRM integration.
```

## 🔧 Troubleshooting

### Common Issues

#### 1. "Server is not running"
**Solution:**
- Ensure LM Studio is open
- Go to "Local Server" tab and click "Start Server"
- Check if port 1234 is available

#### 2. "No models found"
**Solution:**
- Download a model in the "Discover" tab
- Load the model in the "Chat" tab
- Return to "Local Server" and select the loaded model

#### 3. "Text generation failed"
**Solution:**
- Check GPU memory usage (should have 4GB+ available)
- Try reloading the model
- Restart LM Studio if needed

#### 4. "API not responsive"
**Solution:**
- Check Windows Firewall/antivirus settings
- Ensure CORS is enabled in LM Studio server settings
- Try restarting the server

### Performance Optimization

#### GPU Memory Management
- **4GB VRAM**: Use Phi-3.1 Mini (4-bit quantized)
- **8GB VRAM**: Use Phi-3.1 Mini or Llama-3.1-8B (4-bit)
- **16GB+ VRAM**: Use larger models or higher precision

#### Model Recommendations by Use Case
- **Email Responses**: `microsoft/Phi-3.1-mini-4K-Instruct` (Fast, efficient)
- **Technical Support**: `codellama/CodeLlama-7b-Instruct` (Code-aware)
- **High Quality**: `meta-llama/Llama-3.1-8B-Instruct` (Better responses)

## 🔄 Integration with CRM

### Automatic Email Response Generation
When a form is submitted, the CRM will:
1. Extract customer information and form data
2. Generate a personalized prompt
3. Send the prompt to LM Studio
4. Receive AI-generated response
5. Format and send the email to the customer

### Example AI-Generated Response
**Input**: Consultation request from John Doe for ML trading system
**Output**:
```
Dear John,

Thank you for your interest in my Real-Time ML Systems architecture services. I've received your consultation request regarding ML trading systems for cryptocurrency markets.

Based on your requirements, I can help you design and implement a robust trading system that leverages machine learning for market analysis and automated decision-making. My experience with PyTorch, SciPy, and financial data processing makes me well-suited for this project.

I'd be happy to schedule a consultation to discuss your specific needs, timeline, and technical requirements. Please let me know your preferred time for a call this week.

Best regards,
DJ Martin
Real-Time ML Systems Architect
CompTIA A+ & Security+ Certified
Master's in Computer Science

Email: <EMAIL>
```

## 📊 Monitoring & Maintenance

### Performance Monitoring
- Monitor GPU temperature and usage
- Track response times in CRM logs
- Monitor model memory usage

### Regular Maintenance
- Update LM Studio when new versions are available
- Consider updating models for improved performance
- Monitor disk space for model storage

### Backup Considerations
- Models are stored locally (no cloud dependency)
- Back up your model configurations
- Document your optimal settings

## 🔒 Security & Privacy

### Advantages of Local Processing
- **Complete Privacy**: No data sent to external APIs
- **No API Costs**: Unlimited usage without charges
- **Offline Capability**: Works without internet connection
- **Data Control**: All processing happens on your hardware

### Security Best Practices
- Keep LM Studio updated
- Monitor network access to port 1234
- Use firewall rules to restrict access if needed
- Regular security updates for your system

## 🚀 Next Steps

1. **Test the Integration**: Submit a test form and verify AI responses
2. **Customize Prompts**: Modify prompts in `aiService.js` for your needs
3. **Monitor Performance**: Check response times and quality
4. **Scale Up**: Consider larger models as your needs grow

## 📞 Support

If you encounter issues:
1. Run `npm run check-lmstudio` for diagnostics
2. Check LM Studio logs for errors
3. Verify GPU drivers are up to date
4. Consult LM Studio documentation at [https://lmstudio.ai/docs](https://lmstudio.ai/docs)

---

**🎉 Congratulations!** Your CRM now has GPU-powered AI email automation with complete privacy and control.
