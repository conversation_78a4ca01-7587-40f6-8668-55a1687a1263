# ==============================================
# CRM System Test Environment Configuration
# ==============================================

# Database Configuration (Test)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=portfolio_crm_test
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-development-only
JWT_EXPIRES_IN=24h

# Email Configuration (Test Mode)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test-password
ADMIN_EMAIL=<EMAIL>
MOCK_EMAIL_SENDING=true

# AI Configuration (LM Studio)
AI_PROVIDER=lmstudio
LMSTUDIO_URL=http://localhost:1234
LMSTUDIO_MODEL=phi3.1-mini-instruct

# Server Configuration
PORT=3001
NODE_ENV=development
API_BASE_URL=http://localhost:3001

# CORS Configuration
FRONTEND_URLS=http://localhost:3000,http://localhost:5173

# Rate Limiting (Relaxed for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX=50

# Logging Configuration
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_SECURITY_LOGGING=true
ENABLE_API_USAGE_TRACKING=true

# Feature Flags
ENABLE_AI_RESPONSES=true
ENABLE_AUTO_FOLLOW_UP=true
ENABLE_LEAD_SCORING=true

# Development Settings
DEBUG_MODE=true
ENABLE_SEED_DATA=true
