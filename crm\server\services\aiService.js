const axios = require('axios');
const { query } = require('../../config/database');

/**
 * AI Service for email automation and response generation
 * Supports multiple AI providers: LM Studio (local), Ollama (local), OpenAI, Hugging Face
 */
class AIService {
  constructor() {
    this.provider = process.env.AI_PROVIDER || 'lmstudio'; // lmstudio, ollama, openai, huggingface
    this.lmstudioUrl = process.env.LMSTUDIO_URL || 'http://localhost:1234';
    this.lmstudioModel = process.env.LMSTUDIO_MODEL || 'phi3.1-mini-instruct';
    this.ollamaUrl = process.env.OLLAMA_URL || 'http://localhost:11434';
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.huggingfaceApiKey = process.env.HUGGINGFACE_API_KEY;
    this.defaultModel = process.env.AI_MODEL || 'phi3.1-mini-instruct';
  }

  /**
   * Generate email response based on form submission
   */
  async generateEmailResponse(formType, customerData, submissionData) {
    try {
      const prompt = this.buildPrompt(formType, customerData, submissionData);

      switch (this.provider) {
        case 'lmstudio':
          return await this.generateWithLMStudio(prompt);
        case 'ollama':
          return await this.generateWithOllama(prompt);
        case 'openai':
          return await this.generateWithOpenAI(prompt);
        case 'huggingface':
          return await this.generateWithHuggingFace(prompt);
        default:
          throw new Error(`Unsupported AI provider: ${this.provider}`);
      }
    } catch (error) {
      console.error('AI email generation error:', error);
      // Fallback to template-based response
      return await this.getFallbackResponse(formType, customerData);
    }
  }

  /**
   * Build prompt for AI model
   */
  buildPrompt(formType, customerData, submissionData) {
    const customerName = customerData.first_name || customerData.name || 'there';

    const basePrompt = `You are DJ Martin, a Real-Time ML Systems Architect specializing in cryptocurrency and equity markets. You have CompTIA A+ and Security+ certifications and a Master's in Computer Science. You're professional, knowledgeable, and helpful.

Customer Information:
- Name: ${customerName}
- Email: ${customerData.email}
- Company: ${customerData.company || 'Not provided'}

Form Type: ${formType}
Submission Details: ${JSON.stringify(submissionData, null, 2)}

Generate a professional, personalized email response that:
1. Thanks the customer for their inquiry
2. Acknowledges their specific request/needs
3. Provides relevant next steps
4. Maintains a professional but friendly tone
5. Includes your contact information
6. Is concise but comprehensive (200-400 words)

Email Response:`;

    return basePrompt;
  }

  /**
   * Generate response using LM Studio (local GPU-powered LLM)
   */
  async generateWithLMStudio(prompt) {
    try {
      console.log(`🤖 Generating response with LM Studio using model: ${this.lmstudioModel}`);

      const response = await axios.post(`${this.lmstudioUrl}/v1/chat/completions`, {
        model: this.lmstudioModel,
        messages: [
          {
            role: 'system',
            content: 'You are DJ Martin, a professional Real-Time ML Systems Architect specializing in cryptocurrency and equity markets. You have CompTIA A+ and Security+ certifications and a Master\'s in Computer Science. Generate professional, personalized email responses that are helpful and knowledgeable.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
        top_p: 0.9,
        stream: false
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30 second timeout
      });

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const content = response.data.choices[0].message.content.trim();

        return {
          success: true,
          content: content,
          provider: 'lmstudio',
          model: this.lmstudioModel,
          usage: response.data.usage || {}
        };
      } else {
        throw new Error('Invalid response format from LM Studio');
      }
    } catch (error) {
      console.error('LM Studio generation error:', error.message);

      // Provide more specific error messages
      if (error.code === 'ECONNREFUSED') {
        throw new Error('LM Studio server is not running. Please start LM Studio and load a model.');
      } else if (error.response?.status === 404) {
        throw new Error('LM Studio API endpoint not found. Please check if the server is running on the correct port.');
      } else if (error.response?.status === 400) {
        throw new Error(`LM Studio API error: ${error.response.data?.error?.message || 'Bad request'}`);
      }

      throw new Error(`Failed to generate response with LM Studio: ${error.message}`);
    }
  }

  /**
   * Generate response using Ollama (local LLM)
   */
  async generateWithOllama(prompt) {
    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.defaultModel,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          max_tokens: 500
        }
      });

      return {
        success: true,
        content: response.data.response.trim(),
        provider: 'ollama',
        model: this.defaultModel
      };
    } catch (error) {
      console.error('Ollama generation error:', error);
      throw new Error('Failed to generate response with Ollama');
    }
  }

  /**
   * Generate response using OpenAI
   */
  async generateWithOpenAI(prompt) {
    if (!this.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const response = await axios.post('https://api.openai.com/v1/chat/completions', {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are DJ Martin, a professional Real-Time ML Systems Architect. Generate professional email responses.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      }, {
        headers: {
          'Authorization': `Bearer ${this.openaiApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        content: response.data.choices[0].message.content.trim(),
        provider: 'openai',
        model: 'gpt-3.5-turbo'
      };
    } catch (error) {
      console.error('OpenAI generation error:', error);
      throw new Error('Failed to generate response with OpenAI');
    }
  }

  /**
   * Generate response using Hugging Face
   */
  async generateWithHuggingFace(prompt) {
    if (!this.huggingfaceApiKey) {
      throw new Error('Hugging Face API key not configured');
    }

    try {
      const response = await axios.post(
        'https://api-inference.huggingface.co/models/microsoft/DialoGPT-large',
        {
          inputs: prompt,
          parameters: {
            max_length: 500,
            temperature: 0.7,
            do_sample: true
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.huggingfaceApiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        content: response.data[0].generated_text.trim(),
        provider: 'huggingface',
        model: 'microsoft/DialoGPT-large'
      };
    } catch (error) {
      console.error('Hugging Face generation error:', error);
      throw new Error('Failed to generate response with Hugging Face');
    }
  }

  /**
   * Get fallback template-based response
   */
  async getFallbackResponse(formType, customerData) {
    try {
      const templateResult = await query(
        'SELECT content, subject FROM email_templates WHERE template_type = $1 AND is_active = true LIMIT 1',
        [formType]
      );

      if (templateResult.rows.length > 0) {
        const template = templateResult.rows[0];
        const customerName = customerData.first_name || customerData.name || 'there';

        return {
          success: true,
          content: template.content.replace(/\{customer_name\}/g, customerName),
          subject: template.subject.replace(/\{customer_name\}/g, customerName),
          provider: 'template',
          model: 'fallback'
        };
      }

      // Ultimate fallback
      const customerName = customerData.first_name || customerData.name || 'there';
      return {
        success: true,
        content: `Dear ${customerName},

Thank you for your inquiry. I've received your message and will get back to you within 24 hours.

Best regards,
DJ Martin
Real-Time ML Systems Architect
Email: <EMAIL>`,
        subject: `Thank you for your inquiry, ${customerName}`,
        provider: 'fallback',
        model: 'static'
      };
    } catch (error) {
      console.error('Fallback response error:', error);
      throw error;
    }
  }

  /**
   * Classify email intent using AI
   */
  async classifyEmailIntent(emailContent) {
    const prompt = `Classify the following email into one of these categories:
- consultation_request
- technical_support
- service_inquiry
- general_question
- complaint
- compliment

Email content: "${emailContent}"

Category:`;

    try {
      let response;
      switch (this.provider) {
        case 'lmstudio':
          response = await this.generateWithLMStudio(prompt);
          break;
        case 'ollama':
          response = await this.generateWithOllama(prompt);
          break;
        default:
          response = await this.generateWithLMStudio(prompt);
      }
      return response.content.toLowerCase().trim();
    } catch (error) {
      console.error('Email classification error:', error);
      return 'general_question';
    }
  }

  /**
   * Generate follow-up email suggestions
   */
  async generateFollowUpSuggestions(customerHistory) {
    const prompt = `Based on the following customer interaction history, suggest 3 follow-up actions:

${JSON.stringify(customerHistory, null, 2)}

Provide actionable follow-up suggestions:`;

    try {
      let response;
      switch (this.provider) {
        case 'lmstudio':
          response = await this.generateWithLMStudio(prompt);
          break;
        case 'ollama':
          response = await this.generateWithOllama(prompt);
          break;
        default:
          response = await this.generateWithLMStudio(prompt);
      }
      return response.content;
    } catch (error) {
      console.error('Follow-up generation error:', error);
      return 'Schedule a follow-up call to discuss their needs further.';
    }
  }
}

module.exports = new AIService();
