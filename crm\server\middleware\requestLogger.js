/**
 * Request logging middleware for CRM system
 * Provides detailed logging for debugging and monitoring
 */

const { query } = require('../../config/database');

/**
 * Request logger middleware
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const requestId = generateRequestId();
  
  // Add request ID to request object
  req.requestId = requestId;
  
  // Log request start
  const requestLog = {
    requestId,
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    userId: req.user?.id,
    body: shouldLogBody(req) ? sanitizeBody(req.body) : '[HIDDEN]'
  };

  console.log(`📥 [${requestId}] ${req.method} ${req.originalUrl} - Started`);
  
  if (process.env.LOG_LEVEL === 'debug') {
    console.log(`🔍 [${requestId}] Request details:`, JSON.stringify(requestLog, null, 2));
  }

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - startTime;
    
    // Log response
    const responseLog = {
      requestId,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: JSON.stringify(data).length,
      success: res.statusCode < 400
    };

    const statusEmoji = res.statusCode < 400 ? '✅' : res.statusCode < 500 ? '⚠️' : '❌';
    console.log(`📤 [${requestId}] ${req.method} ${req.originalUrl} - ${statusEmoji} ${res.statusCode} (${duration}ms)`);
    
    if (process.env.LOG_LEVEL === 'debug') {
      console.log(`🔍 [${requestId}] Response details:`, JSON.stringify(responseLog, null, 2));
    }

    // Log to database for analytics (async, don't wait)
    if (process.env.ENABLE_REQUEST_LOGGING === 'true') {
      logToDatabase(requestLog, responseLog).catch(error => {
        console.error('Failed to log request to database:', error.message);
      });
    }

    return originalJson.call(this, data);
  };

  next();
};

/**
 * Generate unique request ID
 */
const generateRequestId = () => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Determine if request body should be logged
 */
const shouldLogBody = (req) => {
  // Don't log sensitive endpoints
  const sensitiveEndpoints = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/change-password'
  ];

  if (sensitiveEndpoints.some(endpoint => req.originalUrl.includes(endpoint))) {
    return false;
  }

  // Don't log large payloads
  const contentLength = parseInt(req.get('Content-Length') || '0');
  if (contentLength > 10000) { // 10KB limit
    return false;
  }

  return true;
};

/**
 * Sanitize request body for logging
 */
const sanitizeBody = (body) => {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'currentPassword',
    'newPassword',
    'token',
    'apiKey',
    'secret'
  ];

  const sanitized = { ...body };

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });

  return sanitized;
};

/**
 * Log request/response to database for analytics
 */
const logToDatabase = async (requestLog, responseLog) => {
  try {
    await query(`
      INSERT INTO analytics_events (event_type, event_data, user_id)
      VALUES ('api_request', $1, $2)
    `, [
      JSON.stringify({
        ...requestLog,
        ...responseLog,
        type: 'api_request'
      }),
      requestLog.userId
    ]);
  } catch (error) {
    // Don't throw error to avoid affecting main request
    console.error('Database logging error:', error.message);
  }
};

/**
 * Performance monitoring middleware
 */
const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  
  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    // Log slow requests
    if (duration > 1000) { // Requests taking more than 1 second
      console.warn(`🐌 [${req.requestId}] Slow request detected: ${req.method} ${req.originalUrl} took ${duration.toFixed(2)}ms`);
    }
    
    // Log to performance metrics
    if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
      logPerformanceMetric(req, res, duration).catch(error => {
        console.error('Performance logging error:', error.message);
      });
    }
  });
  
  next();
};

/**
 * Log performance metrics to database
 */
const logPerformanceMetric = async (req, res, duration) => {
  try {
    await query(`
      INSERT INTO analytics_events (event_type, event_data)
      VALUES ('performance_metric', $1)
    `, [
      JSON.stringify({
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration: duration,
        timestamp: new Date().toISOString(),
        userAgent: req.get('User-Agent'),
        ip: req.ip
      })
    ]);
  } catch (error) {
    console.error('Performance metric logging error:', error.message);
  }
};

/**
 * Security event logger
 */
const securityLogger = (event, req, details = {}) => {
  const securityLog = {
    event,
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id,
    details
  };

  console.warn(`🔒 Security Event: ${event}`, JSON.stringify(securityLog, null, 2));

  // Log to database
  if (process.env.ENABLE_SECURITY_LOGGING === 'true') {
    query(`
      INSERT INTO analytics_events (event_type, event_data, user_id)
      VALUES ('security_event', $1, $2)
    `, [JSON.stringify(securityLog), req.user?.id]).catch(error => {
      console.error('Security logging error:', error.message);
    });
  }
};

/**
 * API usage tracker
 */
const apiUsageTracker = (req, res, next) => {
  // Track API endpoint usage
  const endpoint = `${req.method} ${req.route?.path || req.originalUrl}`;
  
  res.on('finish', () => {
    if (process.env.ENABLE_API_USAGE_TRACKING === 'true') {
      trackApiUsage(endpoint, req, res).catch(error => {
        console.error('API usage tracking error:', error.message);
      });
    }
  });
  
  next();
};

/**
 * Track API usage statistics
 */
const trackApiUsage = async (endpoint, req, res) => {
  try {
    await query(`
      INSERT INTO analytics_events (event_type, event_data, user_id)
      VALUES ('api_usage', $1, $2)
    `, [
      JSON.stringify({
        endpoint,
        method: req.method,
        statusCode: res.statusCode,
        timestamp: new Date().toISOString(),
        userRole: req.user?.role,
        ip: req.ip
      }),
      req.user?.id
    ]);
  } catch (error) {
    console.error('API usage tracking error:', error.message);
  }
};

module.exports = {
  requestLogger,
  performanceMonitor,
  securityLogger,
  apiUsageTracker
};
