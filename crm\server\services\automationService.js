const { query } = require('../../config/database');
const aiService = require('./aiService');
const emailService = require('./emailService');

/**
 * Automation service for handling business rules and workflows
 */
class AutomationService {
  
  /**
   * Trigger automation based on event type
   */
  async triggerAutomation(eventType, eventData) {
    try {
      console.log(`🤖 Triggering automation for event: ${eventType}`);
      
      // Get active automation rules for this event type
      const rules = await this.getActiveRules(eventType);
      
      for (const rule of rules) {
        try {
          await this.executeRule(rule, eventData);
        } catch (error) {
          console.error(`❌ Failed to execute rule ${rule.id}:`, error.message);
        }
      }
      
    } catch (error) {
      console.error('Automation trigger error:', error);
    }
  }

  /**
   * Get active automation rules for event type
   */
  async getActiveRules(eventType) {
    try {
      const result = await query(`
        SELECT * FROM automation_rules 
        WHERE trigger_type = $1 AND is_active = true
        ORDER BY created_at
      `, [eventType]);
      
      return result.rows || [];
    } catch (error) {
      console.error('Error fetching automation rules:', error);
      return [];
    }
  }

  /**
   * Execute automation rule
   */
  async executeRule(rule, eventData) {
    try {
      const conditions = JSON.parse(rule.trigger_conditions);
      const actions = JSON.parse(rule.actions);
      
      // Check if conditions are met
      if (!this.checkConditions(conditions, eventData)) {
        return;
      }
      
      console.log(`✅ Executing automation rule: ${rule.name}`);
      
      // Execute actions
      for (const action of actions) {
        await this.executeAction(action, eventData);
      }
      
      // Update last executed timestamp
      await query(
        'UPDATE automation_rules SET last_executed = CURRENT_TIMESTAMP WHERE id = $1',
        [rule.id]
      );
      
    } catch (error) {
      console.error(`Rule execution error for ${rule.name}:`, error);
    }
  }

  /**
   * Check if conditions are met
   */
  checkConditions(conditions, eventData) {
    try {
      // Simple condition checking - can be expanded
      for (const condition of conditions) {
        const { field, operator, value } = condition;
        const fieldValue = this.getNestedValue(eventData, field);
        
        switch (operator) {
          case 'equals':
            if (fieldValue !== value) return false;
            break;
          case 'contains':
            if (!fieldValue || !fieldValue.toString().includes(value)) return false;
            break;
          case 'exists':
            if (fieldValue === undefined || fieldValue === null) return false;
            break;
          default:
            console.warn(`Unknown operator: ${operator}`);
        }
      }
      
      return true;
    } catch (error) {
      console.error('Condition checking error:', error);
      return false;
    }
  }

  /**
   * Execute automation action
   */
  async executeAction(action, eventData) {
    try {
      switch (action.type) {
        case 'send_email':
          await this.sendAutomatedEmail(action, eventData);
          break;
        case 'update_status':
          await this.updateCustomerStatus(action, eventData);
          break;
        case 'create_task':
          await this.createTask(action, eventData);
          break;
        case 'send_notification':
          await this.sendNotification(action, eventData);
          break;
        default:
          console.warn(`Unknown action type: ${action.type}`);
      }
    } catch (error) {
      console.error(`Action execution error for ${action.type}:`, error);
    }
  }

  /**
   * Send automated email
   */
  async sendAutomatedEmail(action, eventData) {
    try {
      const { template_id, use_ai, delay_minutes = 0 } = action.config;
      
      // Get customer information
      const customerId = eventData.customer_id;
      if (!customerId) {
        console.warn('No customer ID provided for email automation');
        return;
      }
      
      const customerResult = await query(
        'SELECT * FROM customers WHERE id = $1',
        [customerId]
      );
      
      if (customerResult.rows.length === 0) {
        console.warn(`Customer not found: ${customerId}`);
        return;
      }
      
      const customer = customerResult.rows[0];
      
      // Generate email content
      let emailContent, emailSubject;
      
      if (use_ai) {
        // Use AI to generate email
        const aiResponse = await aiService.generateEmailResponse(
          eventData.form_type || 'general_inquiry',
          customer,
          eventData.submission_data || {}
        );
        
        if (aiResponse.success) {
          emailContent = aiResponse.content;
          emailSubject = aiResponse.subject || `Thank you for your ${eventData.form_type || 'inquiry'}`;
        }
      }
      
      // Fallback to template if AI fails or not used
      if (!emailContent && template_id) {
        const templateResult = await query(
          'SELECT * FROM email_templates WHERE id = $1',
          [template_id]
        );
        
        if (templateResult.rows.length > 0) {
          const template = templateResult.rows[0];
          emailSubject = this.replaceVariables(template.subject, customer, eventData);
          emailContent = this.replaceVariables(template.content, customer, eventData);
        }
      }
      
      if (!emailContent) {
        console.warn('No email content generated for automation');
        return;
      }
      
      // Send email (with delay if specified)
      if (delay_minutes > 0) {
        setTimeout(async () => {
          await this.sendEmail(customer, emailSubject, emailContent, eventData);
        }, delay_minutes * 60 * 1000);
      } else {
        await this.sendEmail(customer, emailSubject, emailContent, eventData);
      }
      
    } catch (error) {
      console.error('Automated email error:', error);
    }
  }

  /**
   * Send email and record communication
   */
  async sendEmail(customer, subject, content, eventData) {
    try {
      // Send email
      await emailService.sendEmail({
        to: customer.email,
        subject: subject,
        html: emailService.formatEmailContent(content, customer.first_name),
        from: process.env.EMAIL_USER
      });
      
      // Record communication
      await query(`
        INSERT INTO communications (
          customer_id, communication_type, direction, subject, content,
          recipient_email, status, created_at
        )
        VALUES ($1, 'email', 'outbound', $2, $3, $4, 'sent', CURRENT_TIMESTAMP)
      `, [customer.id, subject, content, customer.email]);
      
      console.log(`📧 Automated email sent to ${customer.email}`);
      
    } catch (error) {
      console.error('Email sending error:', error);
    }
  }

  /**
   * Update customer status
   */
  async updateCustomerStatus(action, eventData) {
    try {
      const { status } = action.config;
      const customerId = eventData.customer_id;
      
      if (!customerId || !status) {
        console.warn('Missing customer ID or status for automation');
        return;
      }
      
      await query(
        'UPDATE customers SET lead_status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [status, customerId]
      );
      
      console.log(`📊 Customer status updated to: ${status}`);
      
    } catch (error) {
      console.error('Status update error:', error);
    }
  }

  /**
   * Create task (placeholder for future task management)
   */
  async createTask(action, eventData) {
    try {
      console.log('📝 Task creation automation triggered (not implemented yet)');
      // TODO: Implement task creation when task management is added
    } catch (error) {
      console.error('Task creation error:', error);
    }
  }

  /**
   * Send notification (placeholder)
   */
  async sendNotification(action, eventData) {
    try {
      console.log('🔔 Notification automation triggered (not implemented yet)');
      // TODO: Implement notifications (Slack, Discord, etc.)
    } catch (error) {
      console.error('Notification error:', error);
    }
  }

  /**
   * Replace variables in template
   */
  replaceVariables(template, customer, eventData) {
    let result = template;
    
    // Customer variables
    result = result.replace(/\{customer_name\}/g, customer.first_name || customer.email);
    result = result.replace(/\{customer_email\}/g, customer.email);
    result = result.replace(/\{customer_company\}/g, customer.company || '');
    
    // Event data variables
    if (eventData.submission_data) {
      const submissionData = typeof eventData.submission_data === 'string' 
        ? JSON.parse(eventData.submission_data) 
        : eventData.submission_data;
        
      Object.keys(submissionData).forEach(key => {
        const placeholder = `{${key}}`;
        result = result.replace(new RegExp(placeholder, 'g'), submissionData[key] || '');
      });
    }
    
    return result;
  }

  /**
   * Get nested value from object
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Create default automation rules
   */
  async createDefaultRules() {
    try {
      const defaultRules = [
        {
          name: 'Welcome Email for New Consultations',
          trigger_type: 'form_submission',
          trigger_conditions: JSON.stringify([
            { field: 'form_type', operator: 'equals', value: 'consultation_request' }
          ]),
          actions: JSON.stringify([
            {
              type: 'send_email',
              config: {
                use_ai: true,
                delay_minutes: 0
              }
            }
          ])
        },
        {
          name: 'Support Request Acknowledgment',
          trigger_type: 'form_submission',
          trigger_conditions: JSON.stringify([
            { field: 'form_type', operator: 'equals', value: 'technical_support_request' }
          ]),
          actions: JSON.stringify([
            {
              type: 'send_email',
              config: {
                use_ai: true,
                delay_minutes: 0
              }
            },
            {
              type: 'update_status',
              config: {
                status: 'contacted'
              }
            }
          ])
        }
      ];

      for (const rule of defaultRules) {
        try {
          await query(`
            INSERT INTO automation_rules (name, trigger_type, trigger_conditions, actions, is_active)
            VALUES ($1, $2, $3, $4, true)
            ON CONFLICT DO NOTHING
          `, [rule.name, rule.trigger_type, rule.trigger_conditions, rule.actions]);
        } catch (error) {
          // Rule might already exist
        }
      }

      console.log('✅ Default automation rules created');
    } catch (error) {
      console.error('Error creating default rules:', error);
    }
  }
}

// Create singleton instance
const automationService = new AutomationService();

// Helper function for triggering automation
const triggerAutomation = async (eventType, eventData) => {
  return await automationService.triggerAutomation(eventType, eventData);
};

module.exports = {
  AutomationService,
  triggerAutomation
};
