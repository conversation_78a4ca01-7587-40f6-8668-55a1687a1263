/**
 * Global error handling middleware for CRM system
 */

/**
 * Custom error class for CRM-specific errors
 */
class CRMError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.name = 'CRMError';
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
  }
}

/**
 * Database error handler
 */
const handleDatabaseError = (error) => {
  console.error('Database error:', error);

  // PostgreSQL specific errors
  if (error.code) {
    switch (error.code) {
      case '23505': // Unique violation
        return new CRMError('Resource already exists', 409, 'DUPLICATE_RESOURCE');
      case '23503': // Foreign key violation
        return new CRMError('Referenced resource not found', 400, 'INVALID_REFERENCE');
      case '23502': // Not null violation
        return new CRMError('Required field is missing', 400, 'MISSING_REQUIRED_FIELD');
      case '22001': // String data too long
        return new CRMError('Input data too long', 400, 'DATA_TOO_LONG');
      case '08006': // Connection failure
        return new CRMError('Database connection failed', 503, 'DATABASE_UNAVAILABLE');
      default:
        return new CRMError('Database operation failed', 500, 'DATABASE_ERROR');
    }
  }

  return new CRMError('Database operation failed', 500, 'DATABASE_ERROR');
};

/**
 * JWT error handler
 */
const handleJWTError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new CRMError('Invalid authentication token', 401, 'INVALID_TOKEN');
  }
  
  if (error.name === 'TokenExpiredError') {
    return new CRMError('Authentication token expired', 401, 'TOKEN_EXPIRED');
  }

  return new CRMError('Authentication failed', 401, 'AUTH_ERROR');
};

/**
 * Validation error handler
 */
const handleValidationError = (error) => {
  if (error.isJoi) {
    const message = error.details.map(detail => detail.message).join(', ');
    return new CRMError(`Validation failed: ${message}`, 400, 'VALIDATION_ERROR');
  }

  return new CRMError('Invalid input data', 400, 'VALIDATION_ERROR');
};

/**
 * AI service error handler
 */
const handleAIError = (error) => {
  console.error('AI service error:', error);

  if (error.message.includes('ECONNREFUSED')) {
    return new CRMError('AI service unavailable', 503, 'AI_SERVICE_UNAVAILABLE');
  }

  if (error.message.includes('timeout')) {
    return new CRMError('AI service timeout', 504, 'AI_SERVICE_TIMEOUT');
  }

  return new CRMError('AI service error', 500, 'AI_SERVICE_ERROR');
};

/**
 * Email service error handler
 */
const handleEmailError = (error) => {
  console.error('Email service error:', error);

  if (error.message.includes('ECONNREFUSED')) {
    return new CRMError('Email service unavailable', 503, 'EMAIL_SERVICE_UNAVAILABLE');
  }

  if (error.message.includes('authentication')) {
    return new CRMError('Email authentication failed', 500, 'EMAIL_AUTH_ERROR');
  }

  return new CRMError('Email sending failed', 500, 'EMAIL_ERROR');
};

/**
 * Main error handling middleware
 */
const errorHandler = (error, req, res, next) => {
  let handledError = error;

  // Handle different types of errors
  if (error.code && error.code.startsWith('23')) {
    handledError = handleDatabaseError(error);
  } else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    handledError = handleJWTError(error);
  } else if (error.isJoi) {
    handledError = handleValidationError(error);
  } else if (error.message && error.message.includes('AI')) {
    handledError = handleAIError(error);
  } else if (error.message && error.message.includes('email')) {
    handledError = handleEmailError(error);
  } else if (!(error instanceof CRMError)) {
    // Convert unknown errors to CRMError
    handledError = new CRMError(
      process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message,
      500,
      'INTERNAL_ERROR'
    );
  }

  // Log error details
  const errorLog = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    error: {
      name: handledError.name,
      message: handledError.message,
      code: handledError.code,
      statusCode: handledError.statusCode,
      stack: process.env.NODE_ENV === 'development' ? handledError.stack : undefined
    }
  };

  // Log based on severity
  if (handledError.statusCode >= 500) {
    console.error('🚨 Server Error:', JSON.stringify(errorLog, null, 2));
  } else if (handledError.statusCode >= 400) {
    console.warn('⚠️ Client Error:', JSON.stringify(errorLog, null, 2));
  }

  // Send error response
  res.status(handledError.statusCode || 500).json({
    success: false,
    error: {
      message: handledError.message,
      code: handledError.code,
      statusCode: handledError.statusCode,
      timestamp: new Date().toISOString(),
      ...(process.env.NODE_ENV === 'development' && {
        stack: handledError.stack,
        details: error
      })
    }
  });
};

/**
 * 404 handler for unmatched routes
 */
const notFoundHandler = (req, res) => {
  const error = new CRMError(
    `Route ${req.method} ${req.originalUrl} not found`,
    404,
    'ROUTE_NOT_FOUND'
  );

  res.status(404).json({
    success: false,
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * Async error wrapper for route handlers
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Rate limit error handler
 */
const rateLimitHandler = (req, res) => {
  const error = new CRMError(
    'Too many requests, please try again later',
    429,
    'RATE_LIMIT_EXCEEDED'
  );

  res.status(429).json({
    success: false,
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString(),
      retryAfter: req.rateLimit?.resetTime
    }
  });
};

module.exports = {
  CRMError,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  rateLimitHandler,
  handleDatabaseError,
  handleJWTError,
  handleValidationError,
  handleAIError,
  handleEmailError
};
