# ==============================================
# Portfolio CRM System Environment Configuration
# ==============================================

# Database Configuration
# PostgreSQL connection settings
DB_HOST=localhost
DB_PORT=5432
DB_NAME=portfolio_crm
DB_USER=postgres
DB_PASSWORD=your_secure_password_here

# JWT Authentication
# Generate a secure random string for production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-use-256-bit-key
JWT_EXPIRES_IN=24h

# Email Configuration
# Gmail SMTP settings (use App Password, not regular password)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-gmail-app-password
ADMIN_EMAIL=<EMAIL>

# SMTP Alternative (if not using Gmail)
# SMTP_HOST=smtp.your-provider.com
# SMTP_PORT=587
# SMTP_SECURE=false

# AI Configuration
# Choose your AI provider: lmstudio, ollama, openai, huggingface
AI_PROVIDER=lmstudio
AI_MODEL=phi3.1-mini-instruct

# LM Studio Configuration (for local GPU-powered AI) - RECOMMENDED
LMSTUDIO_URL=http://localhost:1234
LMSTUDIO_MODEL=phi3.1-mini-instruct
# Alternative models you can use in LM Studio:
# LMSTUDIO_MODEL=llama-3.1-8b-instruct
# LMSTUDIO_MODEL=mistral-7b-instruct
# LMSTUDIO_MODEL=codellama-7b-instruct

# Ollama Configuration (alternative local AI)
OLLAMA_URL=http://localhost:11434

# OpenAI Configuration (if using OpenAI)
# OPENAI_API_KEY=sk-your-openai-api-key-here

# Hugging Face Configuration (if using Hugging Face)
# HUGGINGFACE_API_KEY=hf_your-huggingface-token-here

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
# Add your frontend URLs (comma-separated for multiple)
FRONTEND_URLS=http://localhost:3000,http://localhost:5173,https://djuvanemartin.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Session Configuration
SESSION_SECRET=your-session-secret-key-here

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_PATH=./uploads

# Redis Configuration (optional, for caching)
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your-redis-password

# Webhook Configuration
# STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
# WEBHOOK_SECRET=your-general-webhook-secret

# Monitoring & Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Security Headers
ENABLE_HELMET=true
ENABLE_CORS=true

# Analytics Configuration
GOOGLE_ANALYTICS_ID=G-6DQ36R6NXQ
ENABLE_ANALYTICS=true

# Feature Flags
ENABLE_AI_RESPONSES=true
ENABLE_AUTO_FOLLOW_UP=true
ENABLE_LEAD_SCORING=true

# External Integrations
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
# DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook

# Development Settings
DEBUG_MODE=false
MOCK_EMAIL_SENDING=false
ENABLE_SEED_DATA=false

# Production Settings (uncomment for production)
# NODE_ENV=production
# DB_SSL=true
# FORCE_HTTPS=true
# TRUST_PROXY=true
