import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaSearch, FaFilter, FaSort, FaEye, FaEdit, FaTrash,
  FaEnvelope, FaPhone, FaBuilding, FaCalendar, FaTag
} from 'react-icons/fa';
import './CustomerList.css';

const CustomerList = ({ onStatsUpdate }) => {
  const [customers, setCustomers] = useState([]);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    loadCustomers();
  }, []);

  useEffect(() => {
    filterAndSortCustomers();
  }, [customers, searchTerm, statusFilter, sortBy, sortOrder]);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/customers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('crmToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCustomers(data.data);
      } else {
        console.error('Failed to load customers');
      }
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortCustomers = () => {
    let filtered = [...customers];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.company?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(customer => customer.lead_status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === 'created_at' || sortBy === 'updated_at') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCustomers(filtered);
  };

  const handleStatusChange = async (customerId, newStatus) => {
    try {
      const response = await fetch(`/api/customers/${customerId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('crmToken')}`
        },
        body: JSON.stringify({ lead_status: newStatus })
      });

      if (response.ok) {
        await loadCustomers();
        onStatsUpdate && onStatsUpdate();
      }
    } catch (error) {
      console.error('Error updating customer status:', error);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'new': '#3498db',
      'contacted': '#f39c12',
      'qualified': '#9b59b6',
      'proposal': '#e67e22',
      'negotiation': '#e74c3c',
      'closed_won': '#27ae60',
      'closed_lost': '#95a5a6'
    };
    return colors[status] || '#95a5a6';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading customers...</p>
      </div>
    );
  }

  return (
    <div className="customer-list">
      {/* Filters and Search */}
      <div className="list-controls">
        <div className="search-box">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="filter-controls">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Statuses</option>
            <option value="new">New</option>
            <option value="contacted">Contacted</option>
            <option value="qualified">Qualified</option>
            <option value="proposal">Proposal</option>
            <option value="negotiation">Negotiation</option>
            <option value="closed_won">Closed Won</option>
            <option value="closed_lost">Closed Lost</option>
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
          >
            <option value="created_at-desc">Newest First</option>
            <option value="created_at-asc">Oldest First</option>
            <option value="first_name-asc">Name A-Z</option>
            <option value="first_name-desc">Name Z-A</option>
            <option value="updated_at-desc">Recently Updated</option>
          </select>
        </div>
      </div>

      {/* Customer Table */}
      <div className="customer-table-container">
        <table className="customer-table">
          <thead>
            <tr>
              <th>Customer</th>
              <th>Contact</th>
              <th>Company</th>
              <th>Status</th>
              <th>Created</th>
              <th>Last Contact</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <AnimatePresence>
              {filteredCustomers.map((customer) => (
                <motion.tr
                  key={customer.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <td>
                    <div className="customer-info">
                      <div className="customer-avatar">
                        {(customer.first_name?.[0] || customer.email[0]).toUpperCase()}
                      </div>
                      <div>
                        <div className="customer-name">
                          {customer.first_name} {customer.last_name}
                        </div>
                        <div className="customer-email">{customer.email}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="contact-info">
                      {customer.phone && (
                        <div className="contact-item">
                          <FaPhone /> {customer.phone}
                        </div>
                      )}
                      <div className="contact-item">
                        <FaEnvelope /> {customer.email}
                      </div>
                    </div>
                  </td>
                  <td>
                    {customer.company && (
                      <div className="company-info">
                        <FaBuilding /> {customer.company}
                      </div>
                    )}
                  </td>
                  <td>
                    <select
                      className="status-select"
                      value={customer.lead_status}
                      onChange={(e) => handleStatusChange(customer.id, e.target.value)}
                      style={{ borderColor: getStatusColor(customer.lead_status) }}
                    >
                      <option value="new">New</option>
                      <option value="contacted">Contacted</option>
                      <option value="qualified">Qualified</option>
                      <option value="proposal">Proposal</option>
                      <option value="negotiation">Negotiation</option>
                      <option value="closed_won">Closed Won</option>
                      <option value="closed_lost">Closed Lost</option>
                    </select>
                  </td>
                  <td>
                    <div className="date-info">
                      <FaCalendar />
                      {formatDate(customer.created_at)}
                    </div>
                  </td>
                  <td>
                    {customer.last_contact_date ? (
                      <div className="date-info">
                        <FaCalendar />
                        {formatDate(customer.last_contact_date)}
                      </div>
                    ) : (
                      <span className="no-contact">No contact</span>
                    )}
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="btn-action view"
                        onClick={() => {
                          setSelectedCustomer(customer);
                          setShowModal(true);
                        }}
                        title="View Details"
                      >
                        <FaEye />
                      </button>
                      <button
                        className="btn-action edit"
                        title="Edit Customer"
                      >
                        <FaEdit />
                      </button>
                      <button
                        className="btn-action delete"
                        title="Delete Customer"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </AnimatePresence>
          </tbody>
        </table>
      </div>

      {filteredCustomers.length === 0 && (
        <div className="empty-state">
          <p>No customers found matching your criteria.</p>
        </div>
      )}
    </div>
  );
};

export default CustomerList;
