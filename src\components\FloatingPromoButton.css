.floating-promo-container {
  position: fixed;
  bottom: 30px; /* Aligned with BuyTimeButton */
  right: 30px; /* Aligned with SettingsWheel */
  z-index: 900;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.floating-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3a86ff 0%, #60a5fa 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(58, 134, 255, 0.4);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.floating-button:hover {
  box-shadow: 0 6px 20px rgba(58, 134, 255, 0.6);
}

.button-icon {
  position: relative;
  z-index: 2;
}

.pulse-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(58, 134, 255, 0.4);
  animation: pulse 2s infinite;
  z-index: 1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.floating-menu {
  background: rgba(15, 23, 42, 0.95);
  border-radius: 12px;
  padding: 10px;
  margin-bottom: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(58, 134, 255, 0.3);
  display: flex;
  flex-direction: column;
  gap: 8px;
  backdrop-filter: blur(5px);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #e2e8f0;
  transition: all 0.2s ease;
  background: rgba(15, 23, 42, 0.7);
  width: 100%;
  text-align: left;
}

.menu-item span {
  white-space: nowrap;
}

.menu-item.promo-item {
  color: #3a86ff;
  border-left: 3px solid #3a86ff;
}

.menu-item.promo-item:hover {
  background: rgba(58, 134, 255, 0.1);
}

.menu-item.consultation-item {
  color: #10b981;
  border-left: 3px solid #10b981;
}

.menu-item.consultation-item:hover {
  background: rgba(16, 185, 129, 0.1);
}

.menu-close {
  align-self: flex-end;
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 5px;
  margin-top: 5px;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.menu-close:hover {
  background: rgba(148, 163, 184, 0.1);
  color: #e2e8f0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .floating-promo-container {
    bottom: 20px;
    right: 20px;
  }

  .floating-button {
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
    font-size: 1.2rem;
  }

  .floating-menu {
    position: absolute;
    bottom: 60px;
    right: 0;
    width: 220px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .menu-option {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .floating-promo-container {
    bottom: 18px;
    right: 18px;
  }

  .floating-button {
    width: 52px;
    height: 52px;
    min-width: 52px;
    min-height: 52px;
    font-size: 1.3rem;
  }

  .floating-menu {
    width: 200px;
    bottom: 62px;
    max-height: 55vh;
  }

  .menu-option {
    min-height: 46px;
    padding: 14px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 375px) {
  .floating-promo-container {
    bottom: 16px;
    right: 16px;
  }

  .floating-button {
    width: 54px;
    height: 54px;
    min-width: 54px;
    min-height: 54px;
    font-size: 1.4rem;
  }

  .floating-menu {
    width: 180px;
    bottom: 64px;
    max-height: 50vh;
    right: -10px;
  }

  .menu-option {
    min-height: 48px;
    padding: 16px;
    font-size: 0.85rem;
  }
}
