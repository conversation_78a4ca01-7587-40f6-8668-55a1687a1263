.consultation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.consultation-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-radius: 16px;
  width: 90%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.3);
  padding: 2rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(58, 134, 255, 0.6) rgba(15, 23, 42, 0.3);
}

/* Custom scrollbar for Webkit browsers */
.consultation-container::-webkit-scrollbar {
  width: 6px;
}

.consultation-container::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 10px;
}

.consultation-container::-webkit-scrollbar-thumb {
  background: rgba(58, 134, 255, 0.6);
  border-radius: 10px;
}

.consultation-container::-webkit-scrollbar-thumb:hover {
  background: rgba(58, 134, 255, 0.8);
}

.consultation-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(15, 23, 42, 0.7);
  border: none;
  color: #94a3b8;
  font-size: 1.2rem;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.consultation-close:hover {
  background: rgba(58, 134, 255, 0.2);
  color: #e2e8f0;
  transform: rotate(90deg);
}

.consultation-header {
  text-align: center;
  margin-bottom: 2rem;
}

.consultation-header h2 {
  color: #e2e8f0;
  font-size: 1.8rem;
  margin: 0 0 0.5rem 0;
}

.consultation-subtitle {
  color: #94a3b8;
  font-size: 1rem;
  margin: 0;
}

.consultation-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
}

.consultation-progress::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(58, 134, 255, 0.2);
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(15, 23, 42, 0.8);
  border: 2px solid rgba(58, 134, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.step-label {
  font-size: 0.85rem;
  color: #94a3b8;
}

.progress-step.active .step-number {
  background: rgba(58, 134, 255, 0.2);
  border-color: #3a86ff;
  color: #3a86ff;
}

.progress-step.active .step-label {
  color: #e2e8f0;
}

.progress-step.current .step-number {
  background: #3a86ff;
  color: #fff;
}

.consultation-step {
  margin-bottom: 1rem;
}

.consultation-step h3 {
  color: #3a86ff;
  font-size: 1.3rem;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.consultation-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.consultation-option {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.consultation-option:hover {
  transform: translateY(-5px);
  border-color: rgba(58, 134, 255, 0.4);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.consultation-option.selected {
  border-color: #3a86ff;
  background: rgba(58, 134, 255, 0.1);
}

.consultation-option.popular {
  border-color: #10b981;
  transform: scale(1.05);
  z-index: 1;
}

.consultation-option.popular:hover {
  transform: scale(1.05) translateY(-5px);
}

.popular-tag {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #10b981;
  color: #fff;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  white-space: nowrap;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.option-header h4 {
  color: #e2e8f0;
  font-size: 1.2rem;
  margin: 0;
}

.option-price {
  color: #10b981;
  font-size: 1.3rem;
  font-weight: 700;
}

.option-duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.option-description {
  color: #cbd5e1;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.option-best-for {
  color: #94a3b8;
  font-size: 0.85rem;
  padding-top: 0.8rem;
  border-top: 1px solid rgba(58, 134, 255, 0.1);
  margin-bottom: 0.8rem;
}

.option-includes {
  margin-top: 0.8rem;
  font-size: 0.85rem;
}

.option-includes strong {
  color: #94a3b8;
  display: block;
  margin-bottom: 0.5rem;
}

.option-includes ul {
  margin: 0.5rem 0 0 0;
  padding-left: 1.2rem;
  color: #cbd5e1;
}

.option-includes li {
  margin-bottom: 0.4rem;
  line-height: 1.4;
}

.option-includes li:last-child {
  margin-bottom: 0;
}

.option-selected {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #3a86ff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.consultation-note {
  background: rgba(15, 23, 42, 0.5);
  border-left: 3px solid #3a86ff;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0 8px 8px 0;
}

.consultation-note p {
  color: #cbd5e1;
  font-size: 0.9rem;
  margin: 0;
}

.what-to-expect {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.what-to-expect h4 {
  color: #e2e8f0;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
  text-align: center;
}

.what-to-expect ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #cbd5e1;
}

.what-to-expect li {
  margin-bottom: 0.8rem;
  line-height: 1.5;
}

.what-to-expect li:last-child {
  margin-bottom: 0;
}

.what-to-expect strong {
  color: #3a86ff;
}

.step-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
}

.back-btn,
.next-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn {
  background: rgba(15, 23, 42, 0.5);
  color: #cbd5e1;
  border: 1px solid rgba(203, 213, 225, 0.3);
}

.back-btn:hover {
  background: rgba(15, 23, 42, 0.7);
  color: #e2e8f0;
}

.next-btn {
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  border: 1px solid rgba(58, 134, 255, 0.3);
}

.next-btn:hover:not(:disabled) {
  background: rgba(58, 134, 255, 0.2);
  border-color: rgba(58, 134, 255, 0.5);
}

.next-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.consultation-form {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: span 2;
  margin-bottom: 1.5rem;
}

.form-group label {
  color: #cbd5e1;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 8px;
  padding: 0.8rem;
  color: #e2e8f0;
  font-size: 0.95rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #3a86ff;
  outline: none;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.selected-package-summary {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.selected-package-summary h4 {
  color: #e2e8f0;
  font-size: 1rem;
  margin: 0 0 0.8rem 0;
}

.package-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #cbd5e1;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}

.package-price {
  color: #10b981;
  font-weight: 600;
}

.package-duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.85rem;
}

.payment-note {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
}

.payment-note svg {
  color: #3a86ff;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.payment-note p {
  color: #cbd5e1;
  font-size: 0.9rem;
  margin: 0;
}

.success-step {
  text-align: center;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: #10b981;
  border: 2px solid rgba(16, 185, 129, 0.3);
}

.success-message {
  color: #cbd5e1;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.consultation-summary {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.consultation-summary h4 {
  color: #e2e8f0;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(58, 134, 255, 0.2);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.8rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item span:first-child {
  color: #94a3b8;
  font-size: 0.9rem;
}

.summary-item span:last-child {
  color: #e2e8f0;
  font-weight: 500;
}

.next-steps {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(58, 134, 255, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.next-steps h4 {
  color: #e2e8f0;
  font-size: 1.1rem;
  margin: 0 0 1rem 0;
}

.next-steps ol {
  color: #cbd5e1;
  margin: 0;
  padding-left: 1.5rem;
}

.next-steps li {
  margin-bottom: 0.8rem;
}

.next-steps li:last-child {
  margin-bottom: 0;
}

.close-btn {
  background: rgba(58, 134, 255, 0.1);
  color: #3a86ff;
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 8px;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(58, 134, 255, 0.2);
  border-color: rgba(58, 134, 255, 0.5);
}

/* Responsive styles */
@media (max-width: 768px) {
  .consultation-container {
    padding: 1.5rem;
    width: 95%;
    max-height: 90vh;
  }

  .consultation-header h2 {
    font-size: 1.5rem;
  }

  .step-label {
    display: none;
  }

  .consultation-options {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .consultation-option {
    padding: 1.5rem;
    min-height: 44px;
  }

  .consultation-option.popular {
    transform: scale(1);
  }

  .consultation-option.popular:hover {
    transform: translateY(-5px);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .summary-item {
    flex-direction: column;
    margin-bottom: 1rem;
  }

  .summary-item span:first-child {
    margin-bottom: 0.3rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 44px;
    font-size: 1rem;
    padding: 12px;
  }

  .btn-primary,
  .btn-secondary {
    min-height: 48px;
    padding: 12px 24px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .consultation-container {
    width: 98%;
    padding: 1rem;
    max-height: 95vh;
  }

  .consultation-header h2 {
    font-size: 1.3rem;
  }

  .consultation-option {
    padding: 1.2rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 46px;
    padding: 14px;
  }

  .btn-primary,
  .btn-secondary {
    min-height: 50px;
    padding: 14px 24px;
  }
}

@media (max-width: 375px) {
  .consultation-container {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    padding: 1rem;
  }

  .consultation-overlay {
    align-items: flex-start;
  }

  .consultation-header h2 {
    font-size: 1.2rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: 48px;
    padding: 16px;
    font-size: 1.1rem;
  }

  .btn-primary,
  .btn-secondary {
    min-height: 52px;
    padding: 16px 24px;
    font-size: 1.1rem;
  }
}
