{"name": "portfolio-crm-system", "version": "1.0.0", "description": "Custom CRM system for <PERSON>'s portfolio with AI-powered email automation", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "migrate": "knex migrate:latest --knexfile config/database.js", "migrate:rollback": "knex migrate:rollback --knexfile config/database.js", "seed": "knex seed:run --knexfile config/database.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint server/ --ext .js", "lint:fix": "eslint server/ --ext .js --fix", "check-lmstudio": "node scripts/check-lmstudio.js", "health-check": "node scripts/check-lmstudio.js", "setup-db": "node scripts/setup-database.js", "test-system": "node scripts/test-system.js", "full-setup": "npm run setup-db && npm run check-lmstudio && npm run test-system"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "knex": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "nodemailer": "^6.9.7", "pg": "^8.11.3", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["crm", "customer-relationship-management", "portfolio", "ai-automation", "email-automation", "nodejs", "postgresql", "express"], "author": "DJ <PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/djuvanemartin/portfolio-crm"}}