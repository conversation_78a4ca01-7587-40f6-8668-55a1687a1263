const axios = require('axios');

/**
 * LM Studio Health Check Utility
 * Provides comprehensive testing and diagnostics for LM Studio integration
 */
class LMStudioHealthCheck {
  constructor(baseUrl = 'http://localhost:1234') {
    this.baseUrl = baseUrl;
    this.timeout = 10000; // 10 seconds
  }

  /**
   * Comprehensive health check for LM Studio
   */
  async performHealthCheck() {
    console.log('🔍 Starting LM Studio Health Check...\n');
    
    const results = {
      serverRunning: false,
      modelsAvailable: false,
      apiResponsive: false,
      modelLoaded: false,
      testGeneration: false,
      loadedModels: [],
      errors: []
    };

    try {
      // Test 1: Check if server is running
      console.log('1️⃣ Checking if LM Studio server is running...');
      results.serverRunning = await this.checkServerStatus();
      
      if (!results.serverRunning) {
        results.errors.push('LM Studio server is not running on ' + this.baseUrl);
        this.printResults(results);
        return results;
      }
      console.log('✅ Server is running\n');

      // Test 2: Check available models
      console.log('2️⃣ Checking available models...');
      const models = await this.getAvailableModels();
      results.modelsAvailable = models.length > 0;
      results.loadedModels = models;
      
      if (results.modelsAvailable) {
        console.log(`✅ Found ${models.length} model(s):`);
        models.forEach(model => console.log(`   - ${model.id}`));
        console.log('');
      } else {
        results.errors.push('No models are loaded in LM Studio');
        console.log('❌ No models found\n');
      }

      // Test 3: Check API responsiveness
      console.log('3️⃣ Testing API responsiveness...');
      results.apiResponsive = await this.testApiResponsiveness();
      
      if (results.apiResponsive) {
        console.log('✅ API is responsive\n');
      } else {
        results.errors.push('LM Studio API is not responding properly');
        console.log('❌ API not responsive\n');
      }

      // Test 4: Check if phi3.1-mini-instruct is loaded
      console.log('4️⃣ Checking for phi3.1-mini-instruct model...');
      const phi31Model = models.find(model => 
        model.id.toLowerCase().includes('phi') && 
        model.id.toLowerCase().includes('3.1') &&
        model.id.toLowerCase().includes('mini')
      );
      
      results.modelLoaded = !!phi31Model;
      
      if (results.modelLoaded) {
        console.log(`✅ Phi-3.1 Mini model found: ${phi31Model.id}\n`);
      } else {
        results.errors.push('phi3.1-mini-instruct model is not loaded');
        console.log('❌ Phi-3.1 Mini model not found\n');
      }

      // Test 5: Test text generation
      if (results.modelLoaded && results.apiResponsive) {
        console.log('5️⃣ Testing text generation...');
        results.testGeneration = await this.testTextGeneration(phi31Model.id);
        
        if (results.testGeneration) {
          console.log('✅ Text generation successful\n');
        } else {
          results.errors.push('Text generation test failed');
          console.log('❌ Text generation failed\n');
        }
      }

    } catch (error) {
      results.errors.push(`Health check error: ${error.message}`);
      console.error('❌ Health check failed:', error.message);
    }

    this.printResults(results);
    return results;
  }

  /**
   * Check if LM Studio server is running
   */
  async checkServerStatus() {
    try {
      const response = await axios.get(`${this.baseUrl}/v1/models`, {
        timeout: this.timeout
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get list of available models
   */
  async getAvailableModels() {
    try {
      const response = await axios.get(`${this.baseUrl}/v1/models`, {
        timeout: this.timeout
      });
      return response.data.data || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Test API responsiveness with a simple request
   */
  async testApiResponsiveness() {
    try {
      const response = await axios.get(`${this.baseUrl}/v1/models`, {
        timeout: this.timeout
      });
      return response.status === 200 && response.data;
    } catch (error) {
      return false;
    }
  }

  /**
   * Test text generation with a simple prompt
   */
  async testTextGeneration(modelId) {
    try {
      const response = await axios.post(`${this.baseUrl}/v1/chat/completions`, {
        model: modelId,
        messages: [
          {
            role: 'user',
            content: 'Say "Hello, CRM system!" in a professional manner.'
          }
        ],
        max_tokens: 50,
        temperature: 0.7
      }, {
        timeout: 30000, // 30 seconds for generation
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const generatedText = response.data?.choices?.[0]?.message?.content;
      if (generatedText) {
        console.log(`   Generated text: "${generatedText.trim()}"`);
        return true;
      }
      return false;
    } catch (error) {
      console.log(`   Generation error: ${error.message}`);
      return false;
    }
  }

  /**
   * Print comprehensive results
   */
  printResults(results) {
    console.log('📊 Health Check Results:');
    console.log('========================');
    console.log(`Server Running: ${results.serverRunning ? '✅' : '❌'}`);
    console.log(`Models Available: ${results.modelsAvailable ? '✅' : '❌'}`);
    console.log(`API Responsive: ${results.apiResponsive ? '✅' : '❌'}`);
    console.log(`Phi-3.1 Model Loaded: ${results.modelLoaded ? '✅' : '❌'}`);
    console.log(`Text Generation: ${results.testGeneration ? '✅' : '❌'}`);
    
    if (results.loadedModels.length > 0) {
      console.log('\n📋 Loaded Models:');
      results.loadedModels.forEach(model => {
        console.log(`   - ${model.id}`);
      });
    }

    if (results.errors.length > 0) {
      console.log('\n❌ Issues Found:');
      results.errors.forEach(error => {
        console.log(`   - ${error}`);
      });
      
      console.log('\n🔧 Troubleshooting Tips:');
      this.printTroubleshootingTips(results);
    } else {
      console.log('\n🎉 All checks passed! LM Studio is ready for CRM integration.');
    }
  }

  /**
   * Print troubleshooting tips based on results
   */
  printTroubleshootingTips(results) {
    if (!results.serverRunning) {
      console.log('   1. Start LM Studio application');
      console.log('   2. Go to "Local Server" tab');
      console.log('   3. Click "Start Server"');
      console.log('   4. Ensure port 1234 is not blocked');
    }

    if (!results.modelsAvailable || !results.modelLoaded) {
      console.log('   1. Download phi-3.1-mini-instruct model in LM Studio');
      console.log('   2. Go to "Chat" tab and load the model');
      console.log('   3. Return to "Local Server" tab');
      console.log('   4. Select the loaded model and start server');
    }

    if (!results.apiResponsive) {
      console.log('   1. Check if firewall is blocking port 1234');
      console.log('   2. Restart LM Studio server');
      console.log('   3. Verify CORS is enabled for localhost');
    }

    if (!results.testGeneration) {
      console.log('   1. Ensure sufficient GPU memory is available');
      console.log('   2. Try reloading the model in LM Studio');
      console.log('   3. Check LM Studio logs for errors');
    }
  }
}

module.exports = LMStudioHealthCheck;
