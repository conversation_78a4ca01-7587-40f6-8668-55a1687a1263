const nodemailer = require('nodemailer');
const { query } = require('../../config/database');

/**
 * Email Service for sending emails through SMTP
 */
class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    try {
      // Skip email initialization in mock mode
      if (process.env.MOCK_EMAIL_SENDING === 'true') {
        console.log('Using console transport for emails (development mode)');
        return;
      }

      // Gmail configuration (most common)
      if (process.env.EMAIL_USER && process.env.EMAIL_PASSWORD) {
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD // App password for Gmail
          }
        });
      }
      // Custom SMTP configuration
      else if (process.env.SMTP_HOST) {
        this.transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
          auth: {
            user: process.env.SMTP_USER || process.env.EMAIL_USER,
            pass: process.env.SMTP_PASSWORD || process.env.EMAIL_PASSWORD
          }
        });
      }
      else {
        console.warn('⚠️ Email configuration not found. Email sending will be disabled.');
        return;
      }

      // Verify connection
      this.verifyConnection();
    } catch (error) {
      console.error('❌ Email transporter initialization failed:', error.message);
    }
  }

  /**
   * Verify email connection
   */
  async verifyConnection() {
    if (!this.transporter) return false;

    try {
      await this.transporter.verify();
      console.log('✅ Email service connected successfully');
      return true;
    } catch (error) {
      console.error('❌ Email service connection failed:', error.message);
      return false;
    }
  }

  /**
   * Send email
   */
  async sendEmail({ to, subject, text, html, from, attachments = [] }) {
    // Handle mock mode
    if (process.env.MOCK_EMAIL_SENDING === 'true') {
      console.log(`📧 [MOCK] Email would be sent to ${to}: ${subject}`);
      console.log(`📝 [MOCK] Content preview: ${(html || text).substring(0, 100)}...`);
      return {
        success: true,
        messageId: 'mock-' + Date.now(),
        response: 'Mock email sent successfully'
      };
    }

    if (!this.transporter) {
      throw new Error('Email service not configured');
    }

    try {
      const mailOptions = {
        from: from || process.env.EMAIL_USER,
        to: to,
        subject: subject,
        text: text,
        html: html || text,
        attachments: attachments
      };

      const result = await this.transporter.sendMail(mailOptions);

      console.log(`📧 Email sent successfully to ${to}: ${subject}`);

      return {
        success: true,
        messageId: result.messageId,
        response: result.response
      };
    } catch (error) {
      console.error('❌ Email sending failed:', error.message);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Send bulk emails
   */
  async sendBulkEmails(emails) {
    if (!this.transporter) {
      throw new Error('Email service not configured');
    }

    const results = [];
    const batchSize = 10; // Send in batches to avoid rate limiting

    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);

      const batchPromises = batch.map(async (email) => {
        try {
          const result = await this.sendEmail(email);
          return { ...email, success: true, result };
        } catch (error) {
          return { ...email, success: false, error: error.message };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + batchSize < emails.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Send templated email
   */
  async sendTemplatedEmail(templateId, to, variables = {}) {
    try {
      // Get template from database
      const templateResult = await query(
        'SELECT * FROM email_templates WHERE id = $1 AND is_active = true',
        [templateId]
      );

      if (templateResult.rows.length === 0) {
        throw new Error('Email template not found');
      }

      const template = templateResult.rows[0];

      // Replace variables in subject and content
      let subject = template.subject;
      let content = template.content;

      Object.keys(variables).forEach(key => {
        const placeholder = `{${key}}`;
        subject = subject.replace(new RegExp(placeholder, 'g'), variables[key]);
        content = content.replace(new RegExp(placeholder, 'g'), variables[key]);
      });

      // Send email
      return await this.sendEmail({
        to: to,
        subject: subject,
        html: content
      });
    } catch (error) {
      console.error('Templated email error:', error);
      throw error;
    }
  }

  /**
   * Format email content with professional styling
   */
  formatEmailContent(content, customerName = '') {
    const greeting = customerName ? `Dear ${customerName},` : 'Hello,';

    return `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 0; background-color: #f8f9fa;">
        <!-- Header -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 20px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600;">DJ Martin</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0 0; font-size: 16px;">Real-Time ML Systems Architect</p>
          <div style="margin-top: 15px;">
            <span style="background: rgba(255,255,255,0.2); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 0 5px;">CompTIA A+</span>
            <span style="background: rgba(255,255,255,0.2); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 0 5px;">Security+</span>
            <span style="background: rgba(255,255,255,0.2); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; margin: 0 5px;">MS Computer Science</span>
          </div>
        </div>

        <!-- Content -->
        <div style="background: white; padding: 40px 30px; line-height: 1.6; color: #333;">
          <p style="margin: 0 0 20px 0; font-size: 16px;">${greeting}</p>
          <div style="font-size: 16px;">
            ${content.replace(/\n/g, '<br>')}
          </div>
        </div>

        <!-- Footer -->
        <div style="background: #f8f9fa; padding: 30px; text-align: center; border-radius: 0 0 10px 10px; border-top: 1px solid #e9ecef;">
          <div style="margin-bottom: 20px;">
            <h3 style="color: #495057; margin: 0 0 15px 0; font-size: 18px;">Contact Information</h3>
            <p style="margin: 5px 0; color: #6c757d;">
              <strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;"><EMAIL></a>
            </p>
            <p style="margin: 5px 0; color: #6c757d;">
              <strong>Website:</strong> <a href="https://djuvanemartin.com" style="color: #667eea; text-decoration: none;">djuvanemartin.com</a>
            </p>
          </div>

          <div style="border-top: 1px solid #dee2e6; padding-top: 20px;">
            <p style="margin: 0; color: #868e96; font-size: 14px;">
              Specializing in Cryptocurrency & Equity Market ML Systems
            </p>
            <p style="margin: 5px 0 0 0; color: #adb5bd; font-size: 12px;">
              This email was sent from DJ Martin's Portfolio CRM System
            </p>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Send customer confirmation email
   */
  async sendCustomerConfirmation(customerData, formType) {
    const customerName = customerData.first_name || customerData.name || 'there';

    const confirmationMessages = {
      consultation_request: `Thank you for your consultation request. I've received your inquiry about ${customerData.consultation_type || 'our services'} and will review your requirements carefully. I'll get back to you within 24 hours to discuss your project and schedule a consultation.`,

      technical_support_request: `Thank you for your technical support request. I've received your ${customerData.urgency || 'standard'} priority request and will address it promptly. For urgent issues, I aim to respond within 2-4 hours during business hours.`,

      service_booking: `Thank you for your service booking request. I've received your project details and will prepare a comprehensive proposal. I'll contact you within 24 hours to discuss the scope, timeline, and next steps.`,

      contact_form: `Thank you for reaching out. I've received your message and appreciate your interest in my services. I'll respond to your inquiry within 24 hours.`,

      quick_quote: `Thank you for your quote request. I've received your project details and will prepare a detailed estimate. You can expect to receive your quote within 24-48 hours.`
    };

    const content = confirmationMessages[formType] || confirmationMessages.contact_form;

    const fullContent = `
      ${content}

      <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-left: 4px solid #667eea; border-radius: 5px;">
        <h4 style="margin: 0 0 15px 0; color: #495057;">What happens next?</h4>
        <ul style="margin: 0; padding-left: 20px; color: #6c757d;">
          <li>I'll review your request and requirements</li>
          <li>I'll prepare a personalized response or proposal</li>
          <li>I'll contact you to discuss details and next steps</li>
          <li>We'll schedule a consultation if needed</li>
        </ul>
      </div>

      <p>If you have any urgent questions or need immediate assistance, please don't hesitate to contact me directly.</p>

      <p>Best regards,<br>
      <strong>DJ Martin</strong><br>
      Real-Time ML Systems Architect</p>
    `;

    return await this.sendEmail({
      to: customerData.email,
      subject: `Thank you for your ${formType.replace('_', ' ')}, ${customerName}`,
      html: this.formatEmailContent(fullContent, customerName)
    });
  }

  /**
   * Send admin notification email
   */
  async sendAdminNotification(customerData, submissionData, formType) {
    const adminEmail = process.env.ADMIN_EMAIL;
    if (!adminEmail) return;

    const subject = `New ${formType.replace('_', ' ')} from ${customerData.first_name || customerData.email}`;

    const content = `
      <h2 style="color: #495057; margin-bottom: 20px;">New Form Submission</h2>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3 style="margin: 0 0 15px 0; color: #495057;">Customer Information</h3>
        <p><strong>Name:</strong> ${customerData.first_name} ${customerData.last_name || ''}</p>
        <p><strong>Email:</strong> ${customerData.email}</p>
        <p><strong>Phone:</strong> ${customerData.phone || 'Not provided'}</p>
        <p><strong>Company:</strong> ${customerData.company || 'Not provided'}</p>
      </div>

      <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
        <h3 style="margin: 0 0 15px 0; color: #856404;">Submission Details</h3>
        <p><strong>Form Type:</strong> ${formType}</p>
        <p><strong>Priority:</strong> ${submissionData.priority || 'Medium'}</p>
        <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
      </div>

      <div style="margin-top: 20px;">
        <h3 style="color: #495057;">Form Data:</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 14px;">${JSON.stringify(submissionData, null, 2)}</pre>
      </div>
    `;

    return await this.sendEmail({
      to: adminEmail,
      subject: subject,
      html: content
    });
  }
}

module.exports = new EmailService();
