const Joi = require('joi');

/**
 * Validation utilities for CRM system
 */

/**
 * Email validation
 */
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Password validation
 * Must be at least 8 characters with uppercase, lowercase, number, and special character
 */
const validatePassword = (password) => {
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

/**
 * Phone number validation (flexible format)
 */
const validatePhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)\.]/g, ''));
};

/**
 * Sanitize input data
 */
const sanitizeInput = (data) => {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sanitized = {};
  
  Object.keys(data).forEach(key => {
    const value = data[key];
    
    if (typeof value === 'string') {
      // Remove potentially dangerous characters and trim
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeInput(item) : item
      );
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeInput(value);
    } else {
      sanitized[key] = value;
    }
  });

  return sanitized;
};

/**
 * Form data validation schemas
 */
const formSchemas = {
  consultation_request: Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().min(2).max(100),
    first_name: Joi.string().min(2).max(50),
    last_name: Joi.string().min(2).max(50),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{7,20}$/),
    company: Joi.string().max(255),
    consultation_type: Joi.string().max(100),
    project_description: Joi.string().max(2000),
    budget_range: Joi.string().max(50),
    timeline: Joi.string().max(100),
    preferred_contact: Joi.string().valid('email', 'phone', 'either'),
    form_type: Joi.string().valid('consultation_request').required()
  }),

  technical_support_request: Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().min(2).max(100),
    first_name: Joi.string().min(2).max(50),
    last_name: Joi.string().min(2).max(50),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{7,20}$/),
    company: Joi.string().max(255),
    issue_description: Joi.string().max(2000).required(),
    urgency: Joi.string().valid('low', 'medium', 'high', 'urgent'),
    service_type: Joi.string().max(100),
    system_info: Joi.string().max(500),
    error_messages: Joi.string().max(1000),
    form_type: Joi.string().valid('technical_support_request').required()
  }),

  service_booking: Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().min(2).max(100),
    first_name: Joi.string().min(2).max(50),
    last_name: Joi.string().min(2).max(50),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{7,20}$/),
    company: Joi.string().max(255),
    service_type: Joi.string().max(100).required(),
    project_scope: Joi.string().max(2000),
    budget: Joi.string().max(50),
    timeline: Joi.string().max(100),
    additional_requirements: Joi.string().max(1000),
    form_type: Joi.string().valid('service_booking').required()
  }),

  contact_form: Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().min(2).max(100),
    first_name: Joi.string().min(2).max(50),
    last_name: Joi.string().min(2).max(50),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{7,20}$/),
    company: Joi.string().max(255),
    subject: Joi.string().max(200),
    message: Joi.string().max(2000).required(),
    form_type: Joi.string().valid('contact_form').required()
  }),

  quick_quote: Joi.object({
    email: Joi.string().email().required(),
    name: Joi.string().min(2).max(100),
    first_name: Joi.string().min(2).max(50),
    last_name: Joi.string().min(2).max(50),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{7,20}$/),
    company: Joi.string().max(255),
    project_type: Joi.string().max(100).required(),
    project_description: Joi.string().max(1000).required(),
    budget_range: Joi.string().max(50),
    timeline: Joi.string().max(100),
    form_type: Joi.string().valid('quick_quote').required()
  })
};

/**
 * Validate form data based on form type
 */
const validateFormData = (data) => {
  const formType = data.form_type;
  
  if (!formType) {
    return {
      isValid: false,
      errors: ['form_type is required']
    };
  }

  const schema = formSchemas[formType];
  
  if (!schema) {
    return {
      isValid: false,
      errors: [`Unsupported form type: ${formType}`]
    };
  }

  const { error, value } = schema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });

  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message),
      validData: value
    };
  }

  return {
    isValid: true,
    validData: value
  };
};

/**
 * User registration validation
 */
const validateUserRegistration = (data) => {
  const schema = Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/).required(),
    firstName: Joi.string().min(2).max(50),
    lastName: Joi.string().min(2).max(50),
    role: Joi.string().valid('admin', 'sales', 'support', 'user').default('user')
  });

  const { error, value } = schema.validate(data);

  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message)
    };
  }

  return {
    isValid: true,
    validData: value
  };
};

/**
 * Customer update validation
 */
const validateCustomerUpdate = (data) => {
  const schema = Joi.object({
    first_name: Joi.string().min(2).max(50),
    last_name: Joi.string().min(2).max(50),
    email: Joi.string().email(),
    phone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{7,20}$/),
    company: Joi.string().max(255),
    lead_status: Joi.string().valid('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost'),
    customer_type: Joi.string().valid('lead', 'prospect', 'customer', 'inactive'),
    assigned_to: Joi.string().uuid(),
    tags: Joi.array().items(Joi.string().max(50)),
    notes: Joi.string().max(2000)
  });

  const { error, value } = schema.validate(data, { stripUnknown: true });

  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message)
    };
  }

  return {
    isValid: true,
    validData: value
  };
};

/**
 * Email template validation
 */
const validateEmailTemplate = (data) => {
  const schema = Joi.object({
    name: Joi.string().min(3).max(100).required(),
    subject: Joi.string().min(5).max(255).required(),
    content: Joi.string().min(10).max(10000).required(),
    template_type: Joi.string().max(50).required(),
    variables: Joi.object().default({})
  });

  const { error, value } = schema.validate(data);

  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => detail.message)
    };
  }

  return {
    isValid: true,
    validData: value
  };
};

module.exports = {
  validateEmail,
  validatePassword,
  validatePhone,
  sanitizeInput,
  validateFormData,
  validateUserRegistration,
  validateCustomerUpdate,
  validateEmailTemplate
};
