const express = require('express');
const { query, transaction } = require('../../config/database');
const { validateFormData, sanitizeInput } = require('../utils/validation');
const { triggerAutomation } = require('../services/automationService');
const { logAnalyticsEvent } = require('../services/analyticsService');

const router = express.Router();

/**
 * Process form submission
 * Replaces Formspree functionality
 */
router.post('/submit', async (req, res) => {
  try {
    const formData = req.body;
    
    // Validate required fields
    if (!formData.email || !formData.form_type) {
      return res.status(400).json({
        success: false,
        message: 'Email and form_type are required'
      });
    }

    // Sanitize input data
    const sanitizedData = sanitizeInput(formData);
    
    // Validate form data based on type
    const validationResult = validateFormData(sanitizedData);
    if (!validationResult.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid form data',
        errors: validationResult.errors
      });
    }

    // Process submission in transaction
    const result = await transaction(async (client) => {
      // Find or create customer
      let customer = await client.query(
        'SELECT * FROM customers WHERE email = $1',
        [sanitizedData.email]
      );

      let customerId;
      if (customer.rows.length === 0) {
        // Create new customer
        const newCustomer = await client.query(`
          INSERT INTO customers (email, first_name, last_name, phone, company, lead_source)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING id
        `, [
          sanitizedData.email,
          sanitizedData.name || sanitizedData.first_name || null,
          sanitizedData.last_name || null,
          sanitizedData.phone || null,
          sanitizedData.company || null,
          'website'
        ]);
        customerId = newCustomer.rows[0].id;
      } else {
        customerId = customer.rows[0].id;
        
        // Update customer info if provided
        await client.query(`
          UPDATE customers 
          SET first_name = COALESCE($2, first_name),
              last_name = COALESCE($3, last_name),
              phone = COALESCE($4, phone),
              company = COALESCE($5, company),
              updated_at = CURRENT_TIMESTAMP,
              last_contact_date = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [
          customerId,
          sanitizedData.name || sanitizedData.first_name || null,
          sanitizedData.last_name || null,
          sanitizedData.phone || null,
          sanitizedData.company || null
        ]);
      }

      // Create form submission record
      const submission = await client.query(`
        INSERT INTO form_submissions (customer_id, form_type, submission_data, reference_id)
        VALUES ($1, $2, $3, $4)
        RETURNING id, submitted_at
      `, [
        customerId,
        sanitizedData.form_type,
        JSON.stringify(sanitizedData),
        sanitizedData.reference_id || null
      ]);

      return {
        submissionId: submission.rows[0].id,
        customerId: customerId,
        submittedAt: submission.rows[0].submitted_at
      };
    });

    // Log analytics event
    await logAnalyticsEvent('form_submission', {
      form_type: sanitizedData.form_type,
      customer_id: result.customerId,
      submission_id: result.submissionId
    });

    // Trigger automation rules
    await triggerAutomation('form_submission', {
      form_type: sanitizedData.form_type,
      customer_id: result.customerId,
      submission_id: result.submissionId,
      submission_data: sanitizedData
    });

    res.status(200).json({
      success: true,
      message: 'Form submitted successfully',
      data: {
        submission_id: result.submissionId,
        submitted_at: result.submittedAt
      }
    });

  } catch (error) {
    console.error('Form submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process form submission',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Get form submission by ID
 */
router.get('/submission/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT fs.*, c.email, c.first_name, c.last_name, c.company
      FROM form_submissions fs
      JOIN customers c ON fs.customer_id = c.id
      WHERE fs.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form submission not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve form submission'
    });
  }
});

/**
 * Update form submission status
 */
router.patch('/submission/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, processed_by } = req.body;

    const validStatuses = ['new', 'processing', 'responded', 'completed', 'archived'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const result = await query(`
      UPDATE form_submissions 
      SET status = $1, 
          processed_by = $2,
          processed_at = CASE WHEN $1 != 'new' THEN CURRENT_TIMESTAMP ELSE processed_at END
      WHERE id = $3
      RETURNING *
    `, [status, processed_by, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form submission not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update submission status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update submission status'
    });
  }
});

module.exports = router;
