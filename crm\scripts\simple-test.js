#!/usr/bin/env node

/**
 * Simple CRM Test
 * Basic functionality test without complex dependencies
 */

require('dotenv').config();
const { query } = require('../config/database');

async function runSimpleTest() {
  console.log('🧪 Running Simple CRM Test\n');

  try {
    // Test 1: Database Connection
    console.log('1️⃣ Testing database connection...');
    const dbResult = await query('SELECT 1 as test');
    console.log('✅ Database connected successfully\n');

    // Test 2: Create Test Customer
    console.log('2️⃣ Creating test customer...');
    const email = `test-${Date.now()}@example.com`;
    
    await query(`
      INSERT INTO customers (email, first_name, last_name, lead_source, lead_status)
      VALUES (?, ?, ?, ?, ?)
    `, [email, 'John', 'Doe', 'test', 'new']);
    
    const customerResult = await query('SELECT * FROM customers WHERE email = ?', [email]);
    const customer = customerResult.rows[0];
    console.log(`✅ Customer created: ${customer.email}\n`);

    // Test 3: Create Form Submission
    console.log('3️⃣ Creating form submission...');
    const submissionData = {
      consultation_type: 'ML Trading System',
      message: 'Test consultation request'
    };

    await query(`
      INSERT INTO form_submissions (customer_id, form_type, submission_data, status)
      VALUES (?, ?, ?, ?)
    `, [customer.id, 'consultation_request', JSON.stringify(submissionData), 'new']);

    const submissionResult = await query('SELECT * FROM form_submissions WHERE customer_id = ?', [customer.id]);
    const submission = submissionResult.rows[0];
    console.log(`✅ Form submission created: ${submission.form_type}\n`);

    // Test 4: Create Communication Record
    console.log('4️⃣ Creating communication record...');
    await query(`
      INSERT INTO communications (customer_id, communication_type, direction, subject, content, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      customer.id,
      'email',
      'outbound',
      'Thank you for your consultation request',
      'We have received your request and will respond soon.',
      'draft'
    ]);

    const commResult = await query('SELECT * FROM communications WHERE customer_id = ?', [customer.id]);
    console.log(`✅ Communication record created\n`);

    // Test 5: Verify All Data
    console.log('5️⃣ Verifying all data...');
    const customerCount = await query('SELECT COUNT(*) as count FROM customers');
    const submissionCount = await query('SELECT COUNT(*) as count FROM form_submissions');
    const commCount = await query('SELECT COUNT(*) as count FROM communications');

    console.log(`📊 Database Summary:`);
    console.log(`   Customers: ${customerCount.rows[0].count}`);
    console.log(`   Form Submissions: ${submissionCount.rows[0].count}`);
    console.log(`   Communications: ${commCount.rows[0].count}\n`);

    console.log('🎉 Simple CRM test completed successfully!');
    console.log('\n✅ Core functionality verified:');
    console.log('   ✅ SQLite database working');
    console.log('   ✅ Customer creation working');
    console.log('   ✅ Form submission working');
    console.log('   ✅ Communication logging working');
    console.log('   ✅ Data persistence working');

    console.log('\n📋 Next steps:');
    console.log('   1. Test LM Studio connection separately');
    console.log('   2. Fix server startup issues');
    console.log('   3. Test full AI integration');

  } catch (error) {
    console.error('❌ Simple test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Run test if called directly
if (require.main === module) {
  runSimpleTest().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = { runSimpleTest };
