const express = require('express');
const { query } = require('../../config/database');

const router = express.Router();

/**
 * Get dashboard statistics
 * GET /api/dashboard/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    // Determine date filter based on timeframe
    let dateFilter = '';
    switch (timeframe) {
      case '7d':
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case '30d':
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      case '90d':
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '90 days'";
        break;
      case 'all':
        dateFilter = '';
        break;
      default:
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '30 days'";
    }

    // Get basic statistics
    const statsQueries = await Promise.all([
      // Total customers
      query(`SELECT COUNT(*) as total FROM customers WHERE customer_type != 'inactive'`),
      
      // New leads in timeframe
      query(`SELECT COUNT(*) as new_leads FROM customers WHERE lead_status = 'new' ${dateFilter}`),
      
      // Pending form submissions
      query(`SELECT COUNT(*) as pending FROM form_submissions WHERE status IN ('new', 'processing')`),
      
      // Emails sent in timeframe
      query(`SELECT COUNT(*) as emails_sent FROM communications WHERE communication_type = 'email' AND direction = 'outbound' ${dateFilter}`),
      
      // Conversion rate (closed_won / total leads)
      query(`
        SELECT 
          COUNT(CASE WHEN lead_status = 'closed_won' THEN 1 END) as won,
          COUNT(*) as total
        FROM customers 
        WHERE customer_type != 'inactive' ${dateFilter}
      `),
      
      // Form submissions by type
      query(`
        SELECT 
          form_type,
          COUNT(*) as count
        FROM form_submissions 
        WHERE 1=1 ${dateFilter}
        GROUP BY form_type
        ORDER BY count DESC
      `),
      
      // Lead status distribution
      query(`
        SELECT 
          lead_status,
          COUNT(*) as count
        FROM customers 
        WHERE customer_type != 'inactive'
        GROUP BY lead_status
        ORDER BY count DESC
      `)
    ]);

    const [
      totalCustomers,
      newLeads,
      pendingForms,
      emailsSent,
      conversionData,
      formTypes,
      leadStatuses
    ] = statsQueries;

    // Calculate conversion rate
    const conversionStats = conversionData.rows[0];
    const conversionRate = conversionStats.total > 0 
      ? ((conversionStats.won / conversionStats.total) * 100).toFixed(1)
      : 0;

    res.json({
      success: true,
      data: {
        totalCustomers: parseInt(totalCustomers.rows[0].total),
        newLeads: parseInt(newLeads.rows[0].new_leads),
        pendingForms: parseInt(pendingForms.rows[0].pending),
        emailsSent: parseInt(emailsSent.rows[0].emails_sent),
        conversionRate: parseFloat(conversionRate),
        formSubmissions: formTypes.rows,
        leadStatusDistribution: leadStatuses.rows,
        timeframe
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard statistics'
    });
  }
});

/**
 * Get recent activity
 * GET /api/dashboard/activity
 */
router.get('/activity', async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    // Get recent form submissions
    const recentSubmissions = await query(`
      SELECT 
        fs.id,
        fs.form_type,
        fs.status,
        fs.submitted_at,
        c.first_name,
        c.last_name,
        c.email,
        c.company
      FROM form_submissions fs
      JOIN customers c ON fs.customer_id = c.id
      ORDER BY fs.submitted_at DESC
      LIMIT $1
    `, [limit]);

    // Get recent communications
    const recentCommunications = await query(`
      SELECT 
        comm.id,
        comm.communication_type,
        comm.direction,
        comm.subject,
        comm.created_at,
        c.first_name,
        c.last_name,
        c.email
      FROM communications comm
      JOIN customers c ON comm.customer_id = c.id
      ORDER BY comm.created_at DESC
      LIMIT $1
    `, [limit]);

    // Get recent customer updates
    const recentCustomers = await query(`
      SELECT 
        id,
        first_name,
        last_name,
        email,
        company,
        lead_status,
        created_at,
        updated_at
      FROM customers
      WHERE customer_type != 'inactive'
      ORDER BY updated_at DESC
      LIMIT $1
    `, [limit]);

    res.json({
      success: true,
      data: {
        recentSubmissions: recentSubmissions.rows,
        recentCommunications: recentCommunications.rows,
        recentCustomers: recentCustomers.rows
      }
    });

  } catch (error) {
    console.error('Dashboard activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent activity'
    });
  }
});

/**
 * Get performance metrics
 * GET /api/dashboard/metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = '';
    let groupBy = '';
    
    switch (timeframe) {
      case '7d':
        dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'";
        groupBy = "DATE(created_at)";
        break;
      case '30d':
        dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'";
        groupBy = "DATE(created_at)";
        break;
      case '90d':
        dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'";
        groupBy = "DATE_TRUNC('week', created_at)";
        break;
      default:
        dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'";
        groupBy = "DATE(created_at)";
    }

    // Form submissions over time
    const submissionTrends = await query(`
      SELECT 
        ${groupBy} as date,
        COUNT(*) as submissions
      FROM form_submissions 
      ${dateFilter}
      GROUP BY ${groupBy}
      ORDER BY date
    `);

    // Customer acquisition over time
    const customerTrends = await query(`
      SELECT 
        ${groupBy} as date,
        COUNT(*) as new_customers
      FROM customers 
      ${dateFilter}
      GROUP BY ${groupBy}
      ORDER BY date
    `);

    // Email response rates
    const emailMetrics = await query(`
      SELECT 
        COUNT(*) as total_sent,
        COUNT(CASE WHEN status IN ('delivered', 'opened', 'clicked') THEN 1 END) as delivered,
        COUNT(CASE WHEN status IN ('opened', 'clicked') THEN 1 END) as opened,
        COUNT(CASE WHEN status = 'clicked' THEN 1 END) as clicked
      FROM communications 
      WHERE communication_type = 'email' 
      AND direction = 'outbound'
      ${dateFilter.replace('created_at', 'created_at')}
    `);

    // Average response time
    const responseTime = await query(`
      SELECT 
        AVG(EXTRACT(EPOCH FROM (processed_at - submitted_at))/3600) as avg_response_hours
      FROM form_submissions 
      WHERE processed_at IS NOT NULL
      ${dateFilter.replace('created_at', 'submitted_at')}
    `);

    const emailStats = emailMetrics.rows[0];
    const deliveryRate = emailStats.total_sent > 0 
      ? ((emailStats.delivered / emailStats.total_sent) * 100).toFixed(1)
      : 0;
    const openRate = emailStats.total_sent > 0 
      ? ((emailStats.opened / emailStats.total_sent) * 100).toFixed(1)
      : 0;
    const clickRate = emailStats.total_sent > 0 
      ? ((emailStats.clicked / emailStats.total_sent) * 100).toFixed(1)
      : 0;

    res.json({
      success: true,
      data: {
        submissionTrends: submissionTrends.rows,
        customerTrends: customerTrends.rows,
        emailMetrics: {
          totalSent: parseInt(emailStats.total_sent),
          deliveryRate: parseFloat(deliveryRate),
          openRate: parseFloat(openRate),
          clickRate: parseFloat(clickRate)
        },
        averageResponseTime: parseFloat(responseTime.rows[0]?.avg_response_hours || 0).toFixed(1),
        timeframe
      }
    });

  } catch (error) {
    console.error('Dashboard metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve performance metrics'
    });
  }
});

/**
 * Get top performing content/forms
 * GET /api/dashboard/top-performers
 */
router.get('/top-performers', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = '';
    switch (timeframe) {
      case '7d':
        dateFilter = "AND submitted_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case '30d':
        dateFilter = "AND submitted_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      case '90d':
        dateFilter = "AND submitted_at >= CURRENT_DATE - INTERVAL '90 days'";
        break;
      default:
        dateFilter = "AND submitted_at >= CURRENT_DATE - INTERVAL '30 days'";
    }

    // Top form types by conversion
    const topForms = await query(`
      SELECT 
        fs.form_type,
        COUNT(*) as total_submissions,
        COUNT(CASE WHEN c.lead_status = 'closed_won' THEN 1 END) as conversions,
        ROUND(
          (COUNT(CASE WHEN c.lead_status = 'closed_won' THEN 1 END)::float / COUNT(*) * 100), 
          1
        ) as conversion_rate
      FROM form_submissions fs
      JOIN customers c ON fs.customer_id = c.id
      WHERE 1=1 ${dateFilter}
      GROUP BY fs.form_type
      HAVING COUNT(*) >= 5
      ORDER BY conversion_rate DESC, total_submissions DESC
      LIMIT 10
    `);

    // Top lead sources
    const topSources = await query(`
      SELECT 
        lead_source,
        COUNT(*) as total_leads,
        COUNT(CASE WHEN lead_status = 'closed_won' THEN 1 END) as conversions,
        ROUND(
          (COUNT(CASE WHEN lead_status = 'closed_won' THEN 1 END)::float / COUNT(*) * 100), 
          1
        ) as conversion_rate
      FROM customers
      WHERE customer_type != 'inactive' ${dateFilter.replace('submitted_at', 'created_at')}
      GROUP BY lead_source
      ORDER BY conversion_rate DESC, total_leads DESC
      LIMIT 10
    `);

    res.json({
      success: true,
      data: {
        topForms: topForms.rows,
        topSources: topSources.rows,
        timeframe
      }
    });

  } catch (error) {
    console.error('Top performers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve top performers'
    });
  }
});

module.exports = router;
