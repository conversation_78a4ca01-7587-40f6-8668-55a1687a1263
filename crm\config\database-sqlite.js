const sqlite3 = require('sqlite3').verbose();
const path = require('path');
require('dotenv').config();

// SQLite database configuration for testing
const dbPath = path.join(__dirname, '..', 'data', 'crm_test.db');

class SQLiteDatabase {
  constructor() {
    this.db = null;
  }

  async connect() {
    if (this.db) return; // Already connected

    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('❌ SQLite connection failed:', err.message);
          reject(err);
        } else {
          console.log('✅ SQLite database connected successfully');
          resolve();
        }
      });
    });
  }

  async query(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not connected'));
        return;
      }

      // Convert PostgreSQL-style $1, $2 to SQLite-style ?
      const sqliteSQL = sql.replace(/\$(\d+)/g, '?');

      if (sql.trim().toUpperCase().startsWith('SELECT')) {
        this.db.all(sqliteSQL, params, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({ rows, rowCount: rows.length });
          }
        });
      } else {
        this.db.run(sqliteSQL, params, function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({
              rows: [],
              rowCount: this.changes,
              lastID: this.lastID
            });
          }
        });
      }
    });
  }

  async transaction(callback) {
    return new Promise(async (resolve, reject) => {
      try {
        await this.query('BEGIN TRANSACTION');

        const client = {
          query: this.query.bind(this)
        };

        const result = await callback(client);
        await this.query('COMMIT');
        resolve(result);
      } catch (error) {
        await this.query('ROLLBACK');
        reject(error);
      }
    });
  }

  async testConnection() {
    try {
      if (!this.db) {
        await this.connect();
      }
      await this.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('❌ Database connection test failed:', error.message);
      return false;
    }
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

// Create singleton instance
const sqliteDB = new SQLiteDatabase();

// Initialize connection
sqliteDB.connect().catch(console.error);

// Export compatible interface
module.exports = {
  query: sqliteDB.query.bind(sqliteDB),
  transaction: sqliteDB.transaction.bind(sqliteDB),
  testConnection: sqliteDB.testConnection.bind(sqliteDB),
  pool: sqliteDB, // For compatibility
  knexConfig: null // Not used with SQLite
};
