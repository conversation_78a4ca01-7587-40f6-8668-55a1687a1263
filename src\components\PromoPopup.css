.promo-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.promo-popup-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-radius: 16px;
  width: 85%;
  max-width: 800px; /* Reduced from 900px */
  max-height: 80vh; /* Reduced from 85vh */
  overflow-y: auto;
  position: relative;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  border: 0.5px solid rgba(58, 134, 255, 0.2);
  scrollbar-width: thin;
  scrollbar-color: rgba(58, 134, 255, 0.6) rgba(15, 23, 42, 0.3);
}

/* Custom scrollbar for Webkit browsers */
.promo-popup-container::-webkit-scrollbar {
  width: 6px;
}

.promo-popup-container::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 10px;
}

.promo-popup-container::-webkit-scrollbar-thumb {
  background: rgba(58, 134, 255, 0.6);
  border-radius: 10px;
}

.promo-popup-container::-webkit-scrollbar-thumb:hover {
  background: rgba(58, 134, 255, 0.8);
}

.promo-close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(15, 23, 42, 0.7);
  border: none;
  color: #94a3b8;
  font-size: 1.2rem;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.promo-close-btn:hover {
  background: rgba(58, 134, 255, 0.2);
  color: #e2e8f0;
  transform: rotate(90deg);
}

.promo-content {
  padding: 2rem; /* Reduced from 2.5rem */
}

.promo-header {
  text-align: center;
  margin-bottom: 1.5rem; /* Reduced from 2rem */
  position: relative;
}

.promo-icon {
  font-size: 2.2rem; /* Reduced from 2.5rem */
  color: #3a86ff;
  margin-bottom: 0.8rem; /* Reduced from 1rem */
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.promo-header h2 {
  color: #e2e8f0;
  font-size: 1.9rem; /* Reduced from 2.2rem */
  font-weight: 800;
  margin: 0;
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.promo-header h2 .highlight {
  color: #3a86ff;
  position: relative;
  display: inline-block;
}

.promo-header h2 .highlight::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, transparent, #3a86ff, transparent);
}

.promo-body {
  display: flex;
  flex-direction: column; /* Changed to column for better layout with horizontal image */
  gap: 2rem;
  margin-bottom: 2rem;
}

.promo-image-container {
  width: 100%; /* Take full width in column layout */
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.25);
  border: 0.5px solid rgba(58, 134, 255, 0.15);
  height: 280px; /* Reduced from 320px */
  margin: 0 auto;
  max-width: 550px; /* Reduced from 600px */
}

.promo-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* Show the full image without cropping */
  object-position: center; /* Center the image */
  display: block;
  transition: transform 0.5s ease;
  background-color: #0f172a; /* Match the background color */
}

.promo-image-container:hover .promo-image {
  transform: scale(1.05);
}

.promo-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8) 0%, rgba(15, 23, 42, 0) 60%);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 1.5rem;
}

.overlay-icon {
  color: #3a86ff;
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(58, 134, 255, 0.5));
}

.promo-text {
  flex: 1;
}

.promo-tagline {
  color: #e2e8f0;
  font-size: 1.3rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  font-weight: 500;
  border-left: 3px solid #3a86ff;
  padding-left: 1rem;
}

.promo-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(15, 23, 42, 0.5);
  border: 0.5px solid rgba(58, 134, 255, 0.15); /* Reduced border width */
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.feature:hover {
  transform: translateY(-3px);
  border-color: rgba(58, 134, 255, 0.4);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  color: #3a86ff;
  font-size: 1.5rem;
  background: rgba(58, 134, 255, 0.1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text h4 {
  color: #e2e8f0;
  margin: 0 0 0.3rem 0;
  font-size: 1.1rem;
}

.feature-text p {
  color: #94a3b8;
  margin: 0;
  font-size: 0.9rem;
}

.promo-pricing {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.pricing-option {
  flex: 1;
  background: rgba(15, 23, 42, 0.5);
  border: 0.5px solid rgba(58, 134, 255, 0.15); /* Reduced border width */
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-option:hover {
  transform: translateY(-5px);
  border-color: rgba(58, 134, 255, 0.3); /* Reduced border opacity */
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.pricing-option.featured {
  border-color: rgba(58, 134, 255, 0.5); /* Reduced border opacity */
  background: rgba(58, 134, 255, 0.1);
  transform: scale(1.05);
  z-index: 1;
}

.pricing-option.featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.best-value {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #3a86ff;
  color: #fff;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  white-space: nowrap;
}

.pricing-option h4 {
  color: #e2e8f0;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.pricing-option .price {
  color: #10b981;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.pricing-option p {
  color: #94a3b8;
  font-size: 0.85rem;
  margin: 0;
}

.promo-cta {
  text-align: center;
  margin-top: 2rem;
}

.cta-button {
  background: linear-gradient(135deg, #3a86ff 0%, #60a5fa 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(58, 134, 255, 0.3);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 20px -3px rgba(58, 134, 255, 0.4);
}

.cta-note {
  color: #94a3b8;
  font-size: 0.9rem;
  margin-top: 1rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .promo-popup-container {
    width: 95%;
    max-height: 90vh;
  }

  .promo-content {
    padding: 1.5rem;
  }

  .promo-header h2 {
    font-size: 1.5rem;
  }

  .promo-body {
    flex-direction: column;
    gap: 1.5rem;
  }

  .promo-image-container {
    height: 200px; /* Smaller height on mobile */
    max-width: 100%;
  }

  .promo-tagline {
    font-size: 1.1rem;
  }

  .promo-pricing {
    flex-direction: column;
    gap: 1rem;
  }

  .pricing-option {
    padding: 1.2rem;
    min-height: 44px;
  }

  .pricing-option.featured {
    transform: scale(1);
  }

  .pricing-option.featured:hover {
    transform: translateY(-5px);
  }

  .cta-button {
    min-height: 48px;
    padding: 12px 24px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .promo-popup-container {
    width: 98%;
    max-height: 95vh;
  }

  .promo-content {
    padding: 1rem;
  }

  .promo-header h2 {
    font-size: 1.3rem;
  }

  .promo-image-container {
    height: 180px;
  }

  .pricing-option {
    padding: 1rem;
  }

  .cta-button {
    min-height: 50px;
    padding: 14px 24px;
  }
}

@media (max-width: 375px) {
  .promo-popup-container {
    width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .promo-popup-overlay {
    align-items: flex-start;
  }

  .promo-content {
    padding: 1rem;
  }

  .promo-header h2 {
    font-size: 1.2rem;
  }

  .promo-image-container {
    height: 160px;
  }

  .pricing-option {
    padding: 0.8rem;
  }

  .cta-button {
    min-height: 52px;
    padding: 16px 24px;
    font-size: 1.1rem;
  }

  .promo-tagline {
    font-size: 1rem;
  }
}
