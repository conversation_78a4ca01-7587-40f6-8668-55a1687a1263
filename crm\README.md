# Portfolio CRM System

A comprehensive Customer Relationship Management system designed specifically for <PERSON>'s portfolio website. This system replaces Formspree with a custom solution featuring AI-powered email automation, advanced analytics, and seamless integration with existing forms.

## 🚀 Features

### Core CRM Functionality
- **Customer Management**: Complete customer lifecycle tracking from lead to conversion
- **Form Integration**: Seamless integration with existing portfolio forms
- **Communication History**: Track all customer interactions in one place
- **Lead Scoring**: Automatic lead qualification and prioritization

### AI-Powered Automation
- **Smart Email Responses**: AI-generated personalized email responses
- **Intent Classification**: Automatic categorization of customer inquiries
- **Follow-up Suggestions**: AI-powered recommendations for next actions
- **Multiple AI Providers**: Support for Ollama (local), OpenAI, and Hugging Face

### Analytics & Reporting
- **Real-time Dashboard**: Live metrics and KPIs
- **Form Analytics**: Track submission rates and conversion funnels
- **Customer Insights**: Detailed customer behavior analysis
- **Export Capabilities**: CSV and PDF report generation

### Security & Performance
- **JWT Authentication**: Secure role-based access control
- **Rate Limiting**: Protection against abuse and spam
- **Data Encryption**: Secure storage of sensitive information
- **Backup & Recovery**: Automated database backups

## 🏗️ Architecture

```
Frontend (React) → API Layer (Node.js/Express) → Database (PostgreSQL) → AI Engine (Local LLM)
```

### Technology Stack
- **Frontend**: React, Framer Motion, CSS3
- **Backend**: Node.js, Express.js, JWT
- **Database**: PostgreSQL with UUID support
- **AI**: Ollama/OpenAI/Hugging Face integration
- **Email**: Nodemailer with SMTP
- **Deployment**: Docker-ready, cloud-compatible

## 📦 Installation

### Prerequisites
- Node.js 16+ and npm 8+
- PostgreSQL 12+
- (Optional) Ollama for local AI processing

### 1. Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd crm

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb portfolio_crm

# Run migrations
npm run migrate

# (Optional) Seed with sample data
npm run seed
```

### 3. Environment Configuration
Edit `.env` file with your settings:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=portfolio_crm
DB_USER=postgres
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
ADMIN_EMAIL=<EMAIL>

# AI Configuration (choose one)
AI_PROVIDER=ollama  # ollama, openai, huggingface
OLLAMA_URL=http://localhost:11434
AI_MODEL=llama2
# OPENAI_API_KEY=your-openai-key
# HUGGINGFACE_API_KEY=your-hf-key

# Server Configuration
PORT=3001
NODE_ENV=development
```

### 4. Start the Application
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 🔧 Integration with Existing Portfolio

### 1. Update Portfolio Environment
Add to your portfolio's `.env` or environment configuration:
```env
REACT_APP_CRM_API_URL=http://localhost:3001/api
```

### 2. Form Integration
The existing forms will automatically use the CRM system. The updated `EmailService.js` handles:
- Primary submission to CRM
- Fallback to existing email service
- Backward compatibility

### 3. CRM Dashboard Access
Access the CRM dashboard at: `http://localhost:3001/dashboard`

Default admin credentials (change immediately):
- Username: `admin`
- Password: `admin123`

## 🤖 AI Configuration

### Local AI with Ollama (Recommended)
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model
ollama pull llama2

# Start Ollama service
ollama serve
```

### OpenAI Integration
```env
AI_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key
```

### Hugging Face Integration
```env
AI_PROVIDER=huggingface
HUGGINGFACE_API_KEY=your-huggingface-token
```

## 📊 API Endpoints

### Public Endpoints
- `POST /api/forms/submit` - Form submission (replaces Formspree)
- `GET /health` - Health check

### Authenticated Endpoints
- `GET /api/customers` - List customers
- `GET /api/forms/submissions` - List form submissions
- `GET /api/analytics/dashboard` - Dashboard statistics
- `POST /api/communications/send` - Send email

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Current user info

## 🚀 Deployment

### Docker Deployment
```bash
# Build Docker image
docker build -t portfolio-crm .

# Run with Docker Compose
docker-compose up -d
```

### Cloud Deployment (Render/Heroku)
1. Set environment variables in your cloud platform
2. Configure PostgreSQL database
3. Deploy the application
4. Run database migrations

### Database Migration in Production
```bash
# Run migrations
npm run migrate

# Check migration status
npx knex migrate:status --knexfile config/database.js
```

## 🔒 Security Considerations

### Production Checklist
- [ ] Change default JWT secret
- [ ] Update default admin credentials
- [ ] Configure HTTPS/SSL
- [ ] Set up database backups
- [ ] Configure rate limiting
- [ ] Enable CORS for production domains
- [ ] Set up monitoring and logging

### Data Privacy
- Customer data is encrypted at rest
- GDPR-compliant data handling
- Secure email transmission
- Audit logging for all actions

## 📈 Monitoring & Analytics

### Built-in Analytics
- Form submission rates
- Customer conversion funnels
- Email open/click rates
- Response time metrics

### External Monitoring
- Health check endpoint for uptime monitoring
- Structured logging for error tracking
- Performance metrics collection

## 🛠️ Development

### Running Tests
```bash
npm test
npm run test:watch
```

### Code Quality
```bash
npm run lint
npm run lint:fix
```

### Database Operations
```bash
# Create new migration
npx knex migrate:make migration_name --knexfile config/database.js

# Rollback migration
npm run migrate:rollback

# Reset database
npm run migrate:rollback --all && npm run migrate
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [Link to detailed docs]
- Issues: [GitHub Issues]

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ for DJ Martin's Portfolio**
