// src/main.jsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './components/App.jsx';
import './index.css';
import 'aos/dist/aos.css';
import AOS from 'aos';

// Configure PDF.js worker
import { pdfjs } from 'react-pdf';
// Use a CDN URL with HTTPS for better compatibility
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
console.log('PDF.js worker configured with version:', pdfjs.version);

// Import PDF styles
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

AOS.init();

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
