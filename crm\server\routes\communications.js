const express = require('express');
const { query, transaction } = require('../../config/database');
const aiService = require('../services/aiService');
const emailService = require('../services/emailService');
const { validateEmail, sanitizeInput } = require('../utils/validation');

const router = express.Router();

/**
 * Get communications for a customer
 * GET /api/communications/customer/:customerId
 */
router.get('/customer/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;
    const { page = 1, limit = 20, type = '' } = req.query;

    const offset = (page - 1) * limit;
    let typeFilter = '';
    const queryParams = [customerId];

    if (type) {
      typeFilter = 'AND communication_type = $2';
      queryParams.push(type);
      queryParams.push(limit, offset);
    } else {
      queryParams.push(limit, offset);
    }

    const communications = await query(`
      SELECT 
        id, communication_type, direction, subject, content,
        sender_email, recipient_email, status, created_at, sent_at,
        created_by
      FROM communications 
      WHERE customer_id = $1 ${typeFilter}
      ORDER BY created_at DESC
      LIMIT $${queryParams.length - 1} OFFSET $${queryParams.length}
    `, queryParams);

    // Get total count
    const countParams = type ? [customerId, type] : [customerId];
    const countQuery = `
      SELECT COUNT(*) 
      FROM communications 
      WHERE customer_id = $1 ${typeFilter}
    `;
    const countResult = await query(countQuery, countParams);
    const totalCount = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: {
        communications: communications.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get communications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve communications'
    });
  }
});

/**
 * Send email to customer
 * POST /api/communications/send-email
 */
router.post('/send-email', async (req, res) => {
  try {
    const { customerId, subject, content, useAI = false } = req.body;
    const userId = req.user.id;

    if (!customerId || !subject) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID and subject are required'
      });
    }

    // Get customer details
    const customerResult = await query(
      'SELECT * FROM customers WHERE id = $1',
      [customerId]
    );

    if (customerResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    const customer = customerResult.rows[0];

    let emailContent = content;

    // Generate AI content if requested and no content provided
    if (useAI && (!content || content.trim().length === 0)) {
      try {
        const aiResponse = await aiService.generateEmailResponse(
          'general_inquiry',
          customer,
          { subject: subject }
        );
        
        if (aiResponse.success) {
          emailContent = aiResponse.content;
        }
      } catch (aiError) {
        console.warn('AI generation failed, using manual content:', aiError.message);
      }
    }

    if (!emailContent || emailContent.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Email content is required'
      });
    }

    // Send email and record communication in transaction
    const result = await transaction(async (client) => {
      // Record communication
      const commResult = await client.query(`
        INSERT INTO communications (
          customer_id, communication_type, direction, subject, content,
          recipient_email, status, created_by
        )
        VALUES ($1, 'email', 'outbound', $2, $3, $4, 'sending', $5)
        RETURNING *
      `, [customerId, subject, emailContent, customer.email, userId]);

      const communication = commResult.rows[0];

      try {
        // Send email
        await emailService.sendEmail({
          to: customer.email,
          subject: subject,
          html: emailContent,
          from: process.env.EMAIL_USER
        });

        // Update status to sent
        await client.query(
          'UPDATE communications SET status = $1, sent_at = CURRENT_TIMESTAMP WHERE id = $2',
          ['sent', communication.id]
        );

        return { ...communication, status: 'sent', sent_at: new Date() };
      } catch (emailError) {
        // Update status to failed
        await client.query(
          'UPDATE communications SET status = $1 WHERE id = $2',
          ['failed', communication.id]
        );
        throw emailError;
      }
    });

    res.json({
      success: true,
      message: 'Email sent successfully',
      data: result
    });

  } catch (error) {
    console.error('Send email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send email',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Generate AI email response
 * POST /api/communications/generate-response
 */
router.post('/generate-response', async (req, res) => {
  try {
    const { customerId, formType = 'general_inquiry', context = {} } = req.body;

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID is required'
      });
    }

    // Get customer details
    const customerResult = await query(
      'SELECT * FROM customers WHERE id = $1',
      [customerId]
    );

    if (customerResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    const customer = customerResult.rows[0];

    // Generate AI response
    const aiResponse = await aiService.generateEmailResponse(
      formType,
      customer,
      context
    );

    if (!aiResponse.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate AI response'
      });
    }

    res.json({
      success: true,
      data: {
        content: aiResponse.content,
        subject: aiResponse.subject || `Re: Your ${formType.replace('_', ' ')}`,
        provider: aiResponse.provider,
        model: aiResponse.model
      }
    });

  } catch (error) {
    console.error('Generate AI response error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI response',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * Add note to customer
 * POST /api/communications/add-note
 */
router.post('/add-note', async (req, res) => {
  try {
    const { customerId, content } = req.body;
    const userId = req.user.id;

    if (!customerId || !content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID and note content are required'
      });
    }

    const result = await query(`
      INSERT INTO communications (
        customer_id, communication_type, direction, content, created_by
      )
      VALUES ($1, 'note', 'outbound', $2, $3)
      RETURNING *
    `, [customerId, content.trim(), userId]);

    res.status(201).json({
      success: true,
      message: 'Note added successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Add note error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add note'
    });
  }
});

/**
 * Get email templates
 * GET /api/communications/templates
 */
router.get('/templates', async (req, res) => {
  try {
    const { type = '' } = req.query;

    let typeFilter = '';
    const queryParams = [];

    if (type) {
      typeFilter = 'WHERE template_type = $1 AND is_active = true';
      queryParams.push(type);
    } else {
      typeFilter = 'WHERE is_active = true';
    }

    const templates = await query(`
      SELECT id, name, subject, content, template_type, variables, created_at
      FROM email_templates 
      ${typeFilter}
      ORDER BY name
    `, queryParams);

    res.json({
      success: true,
      data: templates.rows
    });

  } catch (error) {
    console.error('Get templates error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve email templates'
    });
  }
});

/**
 * Create email template
 * POST /api/communications/templates
 */
router.post('/templates', async (req, res) => {
  try {
    const { name, subject, content, templateType, variables = {} } = req.body;
    const userId = req.user.id;

    if (!name || !subject || !content || !templateType) {
      return res.status(400).json({
        success: false,
        message: 'Name, subject, content, and template type are required'
      });
    }

    const result = await query(`
      INSERT INTO email_templates (name, subject, content, template_type, variables, created_by)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, subject, content, templateType, JSON.stringify(variables), userId]);

    res.status(201).json({
      success: true,
      message: 'Email template created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Create template error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create email template'
    });
  }
});

/**
 * Update communication status
 * PATCH /api/communications/:id/status
 */
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['draft', 'sent', 'delivered', 'opened', 'clicked', 'replied', 'failed'];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const result = await query(`
      UPDATE communications 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `, [status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Communication not found'
      });
    }

    res.json({
      success: true,
      message: 'Communication status updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update communication status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update communication status'
    });
  }
});

module.exports = router;
