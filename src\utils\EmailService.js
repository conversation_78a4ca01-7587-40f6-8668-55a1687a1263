/**
 * Email Service utility for sending confirmation emails
 */

// CRM API for form submissions (replaces Formspree)
const CRM_API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://your-crm-domain.com/api'
  : 'http://localhost:3001/api';

// Fallback to existing email service for backward compatibility
const EMAIL_API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://email-service-1r9l.onrender.com/api/email'
  : 'http://localhost:3000/api/email';

/**
 * Submit form to CRM system (replaces Formspree)
 * @param {Object} data - Form data
 * @returns {Promise} - Response from the CRM API
 */
export const submitToCRM = async (data) => {
  try {
    const response = await fetch(`${CRM_API_BASE_URL}/forms/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (response.ok) {
      return {
        success: true,
        message: 'Form submitted successfully to CRM',
        data: result.data
      };
    } else {
      throw new Error(result.message || 'CRM submission failed');
    }
  } catch (error) {
    console.error('Error submitting to CRM:', error);
    return {
      success: false,
      message: 'Failed to submit to CRM',
      error: error.message
    };
  }
};

/**
 * Send a support request confirmation email
 * @param {Object} data - Support request data
 * @returns {Promise} - Response from the API
 */
export const sendSupportConfirmation = async (data) => {
  try {
    // First try to submit to CRM
    const crmResult = await submitToCRM({
      ...data,
      form_type: 'technical_support_request'
    });

    if (crmResult.success) {
      return crmResult;
    }

    // Fallback to existing email service
    const response = await fetch(`${EMAIL_API_BASE_URL}/support-confirmation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    return await response.json();
  } catch (error) {
    console.error('Error sending support confirmation email:', error);
    return {
      success: false,
      message: 'Failed to send confirmation email',
      error: error.message
    };
  }
};

/**
 * Send a consultation booking confirmation email
 * @param {Object} data - Consultation booking data
 * @returns {Promise} - Response from the API
 */
export const sendConsultationConfirmation = async (data) => {
  try {
    // First try to submit to CRM
    const crmResult = await submitToCRM({
      ...data,
      form_type: 'consultation_request'
    });

    if (crmResult.success) {
      return crmResult;
    }

    // Fallback to existing email service
    const response = await fetch(`${EMAIL_API_BASE_URL}/consultation-confirmation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    return await response.json();
  } catch (error) {
    console.error('Error sending consultation confirmation email:', error);
    return {
      success: false,
      message: 'Failed to send confirmation email',
      error: error.message
    };
  }
};

/**
 * Send a service booking confirmation email
 * @param {Object} data - Service booking data
 * @returns {Promise} - Response from the API
 */
export const sendServiceBookingConfirmation = async (data) => {
  try {
    // First try to submit to CRM
    const crmResult = await submitToCRM({
      ...data,
      form_type: 'service_booking'
    });

    if (crmResult.success) {
      return crmResult;
    }

    // Fallback to existing email service
    const response = await fetch(`${EMAIL_API_BASE_URL}/service-booking-confirmation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    return await response.json();
  } catch (error) {
    console.error('Error sending service booking confirmation email:', error);
    return {
      success: false,
      message: 'Failed to send confirmation email',
      error: error.message
    };
  }
};

/**
 * Send a contact form submission
 * @param {Object} data - Contact form data
 * @returns {Promise} - Response from the API
 */
export const sendContactFormSubmission = async (data) => {
  try {
    // First try to submit to CRM
    const crmResult = await submitToCRM({
      ...data,
      form_type: 'contact_form'
    });

    if (crmResult.success) {
      return crmResult;
    }

    // Fallback to existing email service
    const response = await fetch(`${EMAIL_API_BASE_URL}/contact-form`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    return await response.json();
  } catch (error) {
    console.error('Error sending contact form submission:', error);
    return {
      success: false,
      message: 'Failed to send contact form',
      error: error.message
    };
  }
};

/**
 * Send a quick quote form submission
 * @param {Object} data - Quick quote form data
 * @returns {Promise} - Response from the API
 */
export const sendQuickQuoteSubmission = async (data) => {
  try {
    // First try to submit to CRM
    const crmResult = await submitToCRM({
      ...data,
      form_type: 'quick_quote'
    });

    if (crmResult.success) {
      return crmResult;
    }

    // Fallback to existing email service
    const response = await fetch(`${EMAIL_API_BASE_URL}/quick-quote`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    return await response.json();
  } catch (error) {
    console.error('Error sending quick quote submission:', error);
    return {
      success: false,
      message: 'Failed to send quick quote',
      error: error.message
    };
  }
};

/**
 * Send a form submission to Formspree (backup)
 * @param {Object} data - Form data
 * @param {string} formId - Formspree form ID
 * @returns {Promise} - Response from Formspree
 */
export const sendFormspreeSubmission = async (data, formId) => {
  try {
    const response = await fetch(`https://formspree.io/f/${formId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    return {
      success: response.ok,
      message: response.ok ? 'Form submitted successfully' : 'Failed to submit form'
    };
  } catch (error) {
    console.error('Error submitting form to Formspree:', error);
    return {
      success: false,
      message: 'Failed to submit form',
      error: error.message
    };
  }
};
