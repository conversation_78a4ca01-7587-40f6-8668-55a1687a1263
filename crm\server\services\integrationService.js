const { query } = require('../../config/database');
const aiService = require('./aiService');
const emailService = require('./emailService');

/**
 * Integration service to connect CRM with existing portfolio forms
 * Replaces Formspree functionality with enhanced features
 */
class IntegrationService {
  constructor() {
    this.formEndpoints = {
      'consultation_request': '/api/forms/consultation',
      'technical_support_request': '/api/forms/support',
      'service_booking': '/api/forms/service-booking',
      'contact_form': '/api/forms/contact',
      'quick_quote': '/api/forms/quick-quote'
    };
  }

  /**
   * Process form submission from existing portfolio forms
   */
  async processFormSubmission(formType, formData, req) {
    try {
      // Validate form type
      if (!this.formEndpoints[formType]) {
        throw new Error(`Unsupported form type: ${formType}`);
      }

      // Extract customer information
      const customerData = this.extractCustomerData(formData);
      
      // Find or create customer
      const customer = await this.findOrCreateCustomer(customerData);
      
      // Create form submission record
      const submission = await this.createFormSubmission(customer.id, formType, formData);
      
      // Generate AI-powered email response
      const emailResponse = await this.generateEmailResponse(formType, customer, formData);
      
      // Send confirmation email to customer
      await this.sendCustomerConfirmation(customer, emailResponse, formType);
      
      // Send notification to admin
      await this.sendAdminNotification(customer, submission, formType);
      
      // Log analytics event
      await this.logFormSubmissionEvent(customer.id, submission.id, formType);
      
      return {
        success: true,
        submission_id: submission.id,
        customer_id: customer.id,
        message: 'Form submitted successfully'
      };
      
    } catch (error) {
      console.error('Form processing error:', error);
      throw error;
    }
  }

  /**
   * Extract customer data from form submission
   */
  extractCustomerData(formData) {
    return {
      email: formData.email,
      first_name: formData.name || formData.first_name || null,
      last_name: formData.last_name || null,
      phone: formData.phone || null,
      company: formData.company || null
    };
  }

  /**
   * Find existing customer or create new one
   */
  async findOrCreateCustomer(customerData) {
    try {
      // Check if customer exists
      const existingCustomer = await query(
        'SELECT * FROM customers WHERE email = $1',
        [customerData.email]
      );

      if (existingCustomer.rows.length > 0) {
        // Update existing customer with new information
        const customer = existingCustomer.rows[0];
        await query(`
          UPDATE customers 
          SET first_name = COALESCE($2, first_name),
              last_name = COALESCE($3, last_name),
              phone = COALESCE($4, phone),
              company = COALESCE($5, company),
              updated_at = CURRENT_TIMESTAMP,
              last_contact_date = CURRENT_TIMESTAMP
          WHERE id = $1
          RETURNING *
        `, [
          customer.id,
          customerData.first_name,
          customerData.last_name,
          customerData.phone,
          customerData.company
        ]);

        return customer;
      } else {
        // Create new customer
        const newCustomer = await query(`
          INSERT INTO customers (email, first_name, last_name, phone, company, lead_source, lead_status)
          VALUES ($1, $2, $3, $4, $5, 'website', 'new')
          RETURNING *
        `, [
          customerData.email,
          customerData.first_name,
          customerData.last_name,
          customerData.phone,
          customerData.company
        ]);

        return newCustomer.rows[0];
      }
    } catch (error) {
      console.error('Customer creation error:', error);
      throw error;
    }
  }

  /**
   * Create form submission record
   */
  async createFormSubmission(customerId, formType, formData) {
    try {
      const submission = await query(`
        INSERT INTO form_submissions (customer_id, form_type, submission_data, status, priority)
        VALUES ($1, $2, $3, 'new', $4)
        RETURNING *
      `, [
        customerId,
        formType,
        JSON.stringify(formData),
        this.determinePriority(formType, formData)
      ]);

      return submission.rows[0];
    } catch (error) {
      console.error('Form submission creation error:', error);
      throw error;
    }
  }

  /**
   * Determine priority based on form type and content
   */
  determinePriority(formType, formData) {
    // High priority for urgent support requests
    if (formType === 'technical_support_request' && formData.urgency === 'urgent') {
      return 'urgent';
    }
    
    // High priority for emergency support
    if (formType === 'technical_support_request' && formData.service_type === 'emergency') {
      return 'high';
    }
    
    // Medium priority for consultation requests
    if (formType === 'consultation_request') {
      return 'medium';
    }
    
    return 'medium';
  }

  /**
   * Generate AI-powered email response
   */
  async generateEmailResponse(formType, customer, formData) {
    try {
      const response = await aiService.generateEmailResponse(formType, customer, formData);
      
      // Store the generated response for future reference
      await query(`
        INSERT INTO communications (customer_id, communication_type, direction, subject, content, status, created_at)
        VALUES ($1, 'email', 'outbound', $2, $3, 'draft', CURRENT_TIMESTAMP)
      `, [
        customer.id,
        response.subject || `Thank you for your ${formType.replace('_', ' ')}`,
        response.content
      ]);

      return response;
    } catch (error) {
      console.error('Email response generation error:', error);
      // Return fallback response
      return {
        success: true,
        content: `Dear ${customer.first_name || 'there'},\n\nThank you for your inquiry. I'll get back to you soon.\n\nBest regards,\nDJ Martin`,
        subject: 'Thank you for your inquiry'
      };
    }
  }

  /**
   * Send confirmation email to customer
   */
  async sendCustomerConfirmation(customer, emailResponse, formType) {
    try {
      await emailService.sendEmail({
        to: customer.email,
        subject: emailResponse.subject,
        html: this.formatEmailContent(emailResponse.content, customer),
        from: process.env.EMAIL_USER
      });

      // Update communication record
      await query(`
        UPDATE communications 
        SET status = 'sent', sent_at = CURRENT_TIMESTAMP
        WHERE customer_id = $1 AND status = 'draft'
        ORDER BY created_at DESC
        LIMIT 1
      `, [customer.id]);

    } catch (error) {
      console.error('Customer confirmation email error:', error);
    }
  }

  /**
   * Send notification to admin
   */
  async sendAdminNotification(customer, submission, formType) {
    try {
      const adminEmail = process.env.ADMIN_EMAIL;
      if (!adminEmail) return;

      const subject = `New ${formType.replace('_', ' ')} from ${customer.first_name || customer.email}`;
      const content = `
        <h2>New Form Submission</h2>
        <p><strong>Type:</strong> ${formType}</p>
        <p><strong>Customer:</strong> ${customer.first_name} ${customer.last_name} (${customer.email})</p>
        <p><strong>Company:</strong> ${customer.company || 'Not provided'}</p>
        <p><strong>Phone:</strong> ${customer.phone || 'Not provided'}</p>
        <p><strong>Submission ID:</strong> ${submission.id}</p>
        <p><strong>Priority:</strong> ${submission.priority}</p>
        <hr>
        <h3>Submission Data:</h3>
        <pre>${JSON.stringify(submission.submission_data, null, 2)}</pre>
      `;

      await emailService.sendEmail({
        to: adminEmail,
        subject: subject,
        html: content,
        from: process.env.EMAIL_USER
      });

    } catch (error) {
      console.error('Admin notification email error:', error);
    }
  }

  /**
   * Format email content with proper HTML structure
   */
  formatEmailContent(content, customer) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; text-align: center;">DJ Martin</h1>
          <p style="color: white; margin: 5px 0 0 0; text-align: center; opacity: 0.9;">Real-Time ML Systems Architect</p>
        </div>
        <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          ${content.replace(/\n/g, '<br>')}
        </div>
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
          <p>This email was sent from DJ Martin's portfolio CRM system.</p>
        </div>
      </div>
    `;
  }

  /**
   * Log analytics event
   */
  async logFormSubmissionEvent(customerId, submissionId, formType) {
    try {
      await query(`
        INSERT INTO analytics_events (event_type, event_data, customer_id)
        VALUES ('form_submission', $1, $2)
      `, [
        JSON.stringify({
          form_type: formType,
          submission_id: submissionId,
          timestamp: new Date().toISOString()
        }),
        customerId
      ]);
    } catch (error) {
      console.error('Analytics logging error:', error);
    }
  }

  /**
   * Get form submission statistics
   */
  async getFormStats(timeframe = '30d') {
    try {
      const timeCondition = timeframe === '30d' 
        ? "submitted_at >= CURRENT_DATE - INTERVAL '30 days'"
        : "submitted_at >= CURRENT_DATE - INTERVAL '7 days'";

      const stats = await query(`
        SELECT 
          form_type,
          COUNT(*) as total_submissions,
          COUNT(CASE WHEN status = 'new' THEN 1 END) as pending_submissions,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_submissions
        FROM form_submissions 
        WHERE ${timeCondition}
        GROUP BY form_type
        ORDER BY total_submissions DESC
      `);

      return stats.rows;
    } catch (error) {
      console.error('Form stats error:', error);
      return [];
    }
  }
}

module.exports = new IntegrationService();
