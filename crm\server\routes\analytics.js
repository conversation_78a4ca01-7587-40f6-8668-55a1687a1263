const express = require('express');
const { AnalyticsService } = require('../services/analyticsService');

const router = express.Router();
const analyticsService = new AnalyticsService();

/**
 * Get form analytics
 * GET /api/analytics/forms
 */
router.get('/forms', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const analytics = await analyticsService.getFormAnalytics(timeframe);
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Form analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve form analytics'
    });
  }
});

/**
 * Get customer analytics
 * GET /api/analytics/customers
 */
router.get('/customers', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const analytics = await analyticsService.getCustomerAnalytics(timeframe);
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Customer analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve customer analytics'
    });
  }
});

/**
 * Get communication analytics
 * GET /api/analytics/communications
 */
router.get('/communications', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const analytics = await analyticsService.getCommunicationAnalytics(timeframe);
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Communication analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve communication analytics'
    });
  }
});

/**
 * Get AI analytics
 * GET /api/analytics/ai
 */
router.get('/ai', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const analytics = await analyticsService.getAIAnalytics(timeframe);
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('AI analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve AI analytics'
    });
  }
});

/**
 * Generate comprehensive report
 * GET /api/analytics/report
 */
router.get('/report', async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const report = await analyticsService.generateReport(timeframe);
    
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Report generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate analytics report'
    });
  }
});

module.exports = router;
