const fs = require('fs');
const path = require('path');

/**
 * Initial database schema migration
 */
exports.up = async function(knex) {
  // Read and execute the schema SQL file
  const schemaSQL = fs.readFileSync(
    path.join(__dirname, '..', 'schema.sql'), 
    'utf8'
  );
  
  // Split by semicolon and execute each statement
  const statements = schemaSQL
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);
  
  for (const statement of statements) {
    await knex.raw(statement);
  }
  
  console.log('✅ Initial schema created successfully');
};

exports.down = async function(knex) {
  // Drop all tables in reverse order
  const tables = [
    'analytics_events',
    'automation_rules', 
    'email_templates',
    'communications',
    'form_submissions',
    'customers',
    'users'
  ];
  
  for (const table of tables) {
    await knex.schema.dropTableIfExists(table);
  }
  
  // Drop the UUID extension
  await knex.raw('DROP EXTENSION IF EXISTS "uuid-ossp"');
  
  console.log('✅ Schema rolled back successfully');
};
