.settings-wheel-container {
  position: fixed;
  top: 80px; /* Moved down to avoid scrollbar */
  right: 30px; /* Aligned with FloatingPromoButton */
  z-index: 100;
  pointer-events: none; /* This makes the container not block interactions */
}

.settings-wheel {
  position: relative;
  width: 32px;
  height: 32px;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  box-shadow: 0 0 15px rgba(58, 134, 255, 0.5);
  border: 2px solid rgba(58, 134, 255, 0.7);
  overflow: visible;
  pointer-events: auto; /* Re-enable pointer events for the wheel */
}

.settings-wheel:hover {
  transform: scale(1.1);
}

.settings-wheel.open {
  transform: rotate(180deg) scale(1.2);
}

.cog-icon {
  color: #3a86ff;
  font-size: 18px;
  animation: spin 10s linear infinite;
}

.settings-wheel.open .cog-icon {
  animation: spin-reverse 1s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/* Fire effects */
.fire-effect {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 165, 0, 0.8) 0%, rgba(255, 69, 0, 0.6) 50%, rgba(255, 0, 0, 0) 70%);
  filter: blur(5px);
  opacity: 0.7;
  animation: fire 2s ease-in-out infinite alternate;
  pointer-events: none;
}

.fire-effect-2 {
  width: 90%;
  height: 90%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 140, 0, 0.6) 50%, rgba(255, 69, 0, 0) 70%);
  animation-delay: 0.3s;
}

.fire-effect-3 {
  width: 80%;
  height: 80%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 215, 0, 0.6) 50%, rgba(255, 165, 0, 0) 70%);
  animation-delay: 0.6s;
}

@keyframes fire {
  0% {
    transform: scale(0.9);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Settings Panel */
.settings-panel {
  position: absolute;
  top: 40px;
  right: 0;
  width: 350px;
  background: rgba(15, 23, 42, 0.95);
  border-radius: 12px;
  border: 2px solid rgba(58, 134, 255, 0.7);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  padding: 1rem;
  color: #e2e8f0;
  transform-origin: top right;
  z-index: 99;
  pointer-events: auto; /* Re-enable pointer events for the panel */
  max-height: 80vh;
  overflow-y: auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(58, 134, 255, 0.3);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.settings-header h3 {
  margin: 0;
  color: #3a86ff;
  font-size: 1.2rem;
}

.close-settings {
  background: none;
  border: none;
  color: #e2e8f0;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-settings:hover {
  color: #3a86ff;
}

.settings-section {
  margin-bottom: 1.5rem;
}

.settings-section h4 {
  color: #cbd5e1;
  font-size: 0.9rem;
  margin-bottom: 0.7rem;
}

.interval-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.interval-option {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(58, 134, 255, 0.3);
  border-radius: 4px;
  padding: 0.4rem 0.3rem;
  color: #cbd5e1;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.interval-option:hover {
  background: rgba(30, 41, 59, 1);
  border-color: rgba(58, 134, 255, 0.5);
}

.interval-option.active {
  background: rgba(58, 134, 255, 0.2);
  border-color: rgba(58, 134, 255, 0.8);
  color: #3a86ff;
  font-weight: 600;
}

.settings-footer {
  border-top: 1px solid rgba(58, 134, 255, 0.3);
  padding-top: 0.8rem;
  text-align: center;
  font-size: 0.9rem;
  color: #94a3b8;
}

/* Settings Tabs */
.settings-tabs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(58, 134, 255, 0.3);
  padding-bottom: 0.5rem;
}

.settings-tab {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 0.8rem;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;
}

.settings-tab svg {
  font-size: 1.2rem;
  margin-bottom: 0.3rem;
}

.settings-tab:hover {
  color: #e2e8f0;
}

.settings-tab.active {
  color: #3a86ff;
  position: relative;
}

.settings-tab.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 25%;
  width: 50%;
  height: 3px;
  background: #3a86ff;
  border-radius: 3px;
}

/* Color Settings */
.color-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.color-setting {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.color-setting label {
  font-size: 0.85rem;
  color: #cbd5e1;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background: none;
}

.color-value {
  font-family: monospace;
  font-size: 0.85rem;
  color: #94a3b8;
}

/* Preset Colors */
.preset-colors {
  margin-top: 1rem;
}

.preset-colors h5 {
  font-size: 0.85rem;
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.color-presets {
  display: flex;
  gap: 0.5rem;
}

.color-preset {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.color-preset:hover {
  transform: scale(1.1);
}

.color-preset.dark-blue {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.color-preset.dark-purple {
  background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
}

.color-preset.dark-green {
  background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
}

.color-preset.dark-red {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
}

.color-preset.blue-gold {
  background: linear-gradient(135deg, #3a86ff 0%, #f7931a 100%);
}

.color-preset.green-purple {
  background: linear-gradient(135deg, #10b981 0%, #8b5cf6 100%);
}

.color-preset.pink-cyan {
  background: linear-gradient(135deg, #ec4899 0%, #06b6d4 100%);
}

.color-preset.red-yellow {
  background: linear-gradient(135deg, #ef4444 0%, #facc15 100%);
}

/* Slider Settings */
.slider-setting {
  margin-bottom: 1rem;
}

.slider-setting label {
  display: block;
  font-size: 0.85rem;
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.slider {
  width: 100%;
  height: 6px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 3px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3a86ff;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(58, 134, 255, 0.5);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3a86ff;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(58, 134, 255, 0.5);
  border: none;
}

/* Responsive styles */
@media (max-width: 768px) {
  .settings-wheel-container {
    top: 120px; /* Moved down further on mobile */
  }

  .settings-wheel {
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
  }

  .settings-panel {
    width: 320px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .interval-options {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }

  .interval-option {
    min-height: 44px;
    padding: 12px;
    font-size: 0.9rem;
  }

  .settings-tab {
    font-size: 0.8rem;
    min-height: 44px;
    padding: 12px;
  }

  .settings-tab svg {
    font-size: 1.1rem;
  }

  .color-option,
  .particle-option {
    min-height: 44px;
    min-width: 44px;
  }
}

@media (max-width: 480px) {
  .settings-wheel {
    width: 48px;
    height: 48px;
    min-width: 48px;
    min-height: 48px;
  }

  .settings-panel {
    width: 280px;
    max-height: 65vh;
  }

  .interval-option {
    min-height: 46px;
    padding: 14px;
  }

  .settings-tab {
    min-height: 46px;
    padding: 14px;
  }

  .color-option,
  .particle-option {
    min-height: 46px;
    min-width: 46px;
  }
}

@media (max-width: 375px) {
  .settings-wheel {
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
  }

  .settings-panel {
    width: 260px;
    max-height: 60vh;
    right: 5px;
  }

  .interval-options {
    grid-template-columns: 1fr;
  }

  .interval-option {
    min-height: 48px;
    padding: 16px;
    font-size: 1rem;
  }

  .settings-tab {
    min-height: 48px;
    padding: 16px;
    font-size: 0.9rem;
  }

  .color-option,
  .particle-option {
    min-height: 48px;
    min-width: 48px;
  }
}
