#!/usr/bin/env node

/**
 * LM Studio Health Check CLI Script
 * Run this script to verify LM Studio is properly configured for the CRM system
 */

const LMStudioHealthCheck = require('../server/utils/lmstudioHealthCheck');
require('dotenv').config();

async function main() {
  console.log('🚀 LM Studio Health Check for Portfolio CRM\n');
  
  // Get LM Studio URL from environment or use default
  const lmstudioUrl = process.env.LMSTUDIO_URL || 'http://localhost:1234';
  const expectedModel = process.env.LMSTUDIO_MODEL || 'phi3.1-mini-instruct';
  
  console.log(`🔗 LM Studio URL: ${lmstudioUrl}`);
  console.log(`🤖 Expected Model: ${expectedModel}\n`);
  
  const healthCheck = new LMStudioHealthCheck(lmstudioUrl);
  const results = await healthCheck.performHealthCheck();
  
  // Exit with appropriate code
  const allPassed = results.serverRunning && 
                   results.modelsAvailable && 
                   results.apiResponsive && 
                   results.modelLoaded && 
                   results.testGeneration;
  
  if (allPassed) {
    console.log('\n🎉 LM Studio is ready for CRM integration!');
    process.exit(0);
  } else {
    console.log('\n❌ LM Studio setup needs attention before CRM integration.');
    process.exit(1);
  }
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error.message);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n👋 Health check interrupted');
  process.exit(0);
});

// Run the health check
main().catch(error => {
  console.error('❌ Health check failed:', error.message);
  process.exit(1);
});
