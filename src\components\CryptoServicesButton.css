.crypto-services-button {
  position: fixed;
  left: 30px; /* Aligned with BuyTimeButton */
  top: 80px; /* Moved down to avoid scrollbar */
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f7931a 0%, #f7931a 50%, #2775ca 100%);
  color: white;
  padding: 12px;
  border-radius: 50px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.crypto-services-button:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.crypto-services-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 1.5rem;
}

.bitcoin-icon {
  color: #f7931a;
  margin-right: 2px;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7));
}

.exchange-icon {
  color: white;
  font-size: 1rem;
  margin: 0 2px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
}

.dollar-icon {
  color: #2775ca;
  margin-left: 2px;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7));
}

.crypto-services-label {
  margin-left: 10px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .crypto-services-button {
    left: 10px;
    top: 120px; /* Moved down further on mobile */
    padding: 12px;
    min-height: 44px;
    min-width: 44px;
  }

  .crypto-services-icon {
    font-size: 1.2rem;
  }

  .crypto-services-label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .crypto-services-button {
    left: 8px;
    top: 130px;
    padding: 14px;
    min-height: 48px;
    min-width: 48px;
  }

  .crypto-services-icon {
    font-size: 1.1rem;
  }

  .crypto-services-label {
    font-size: 0.85rem;
  }
}

@media (max-width: 375px) {
  .crypto-services-button {
    left: 6px;
    top: 140px;
    padding: 16px;
    min-height: 50px;
    min-width: 50px;
  }

  .crypto-services-icon {
    font-size: 1rem;
  }

  .crypto-services-label {
    font-size: 0.8rem;
  }
}
